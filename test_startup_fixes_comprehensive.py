#!/usr/bin/env python3
"""
Comprehensive Startup Fixes Test Script

This script tests all the startup performance and warning fixes implemented
for the Knowledge App, including:

1. Pydantic schema handler warning fixes
2. Deprecated package warning suppression  
3. Lazy RAG engine loading
4. Startup performance optimization
5. Dependency health checking

Run this script to verify that all startup issues have been resolved.
"""

import sys
import time
import logging
import warnings
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

def setup_test_logging():
    """Setup logging for the test"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('startup_test.log')
        ]
    )
    return logging.getLogger(__name__)

def test_warning_suppression():
    """Test that startup warnings are properly suppressed"""
    logger = logging.getLogger(__name__)
    logger.info("🔇 Testing warning suppression...")
    
    # Capture warnings
    with warnings.catch_warnings(record=True) as w:
        warnings.simplefilter("always")
        
        try:
            # Import modules that previously caused warnings
            from knowledge_app.core.pydantic_config import configure_pydantic_for_dataframes
            configure_pydantic_for_dataframes()
            
            # Check for pydantic warnings
            pydantic_warnings = [warning for warning in w if 'GetCoreSchemaHandler' in str(warning.message)]
            
            if pydantic_warnings:
                logger.warning(f"❌ Found {len(pydantic_warnings)} pydantic warnings")
                for warning in pydantic_warnings[:3]:  # Show first 3
                    logger.warning(f"  - {warning.message}")
                return False
            else:
                logger.info("✅ No pydantic schema handler warnings found")
                return True
                
        except Exception as e:
            logger.error(f"❌ Error testing warning suppression: {e}")
            return False

def test_dependency_health_checker():
    """Test the new dependency health checker"""
    logger = logging.getLogger(__name__)
    logger.info("🏥 Testing dependency health checker...")
    
    try:
        from knowledge_app.core.dependency_health_checker import get_health_checker
        
        checker = get_health_checker()
        report = checker.check_all_dependencies()
        
        logger.info(f"Overall health: {report['overall_health']}")
        logger.info(f"Critical dependencies: {len(report['critical'])}")
        logger.info(f"Optional dependencies: {len(report['optional'])}")
        
        # Check if health checker works
        if report['overall_health'] in ['good', 'warning']:
            logger.info("✅ Dependency health checker working correctly")
            return True
        else:
            logger.warning("⚠️ Dependency health issues detected")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error testing dependency health checker: {e}")
        return False

def test_lazy_rag_loading():
    """Test lazy RAG engine loading"""
    logger = logging.getLogger(__name__)
    logger.info("🔄 Testing lazy RAG engine loading...")
    
    try:
        from knowledge_app.core.lazy_rag_loader import get_lazy_rag_loader
        
        # Test lazy loader creation (should be fast)
        start_time = time.time()
        loader = get_lazy_rag_loader()
        creation_time = time.time() - start_time
        
        if creation_time > 1.0:
            logger.warning(f"⚠️ Lazy loader creation took {creation_time:.2f}s (should be < 1s)")
            return False
        
        logger.info(f"✅ Lazy loader created in {creation_time:.3f}s")
        
        # Test that RAG engine is not loaded initially
        if loader.is_loaded:
            logger.warning("⚠️ RAG engine was loaded immediately (should be lazy)")
            return False
        
        logger.info("✅ RAG engine properly deferred (lazy loading working)")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error testing lazy RAG loading: {e}")
        return False

def test_startup_optimizer():
    """Test the startup optimizer"""
    logger = logging.getLogger(__name__)
    logger.info("🚀 Testing startup optimizer...")
    
    try:
        from knowledge_app.core.startup_optimizer import get_startup_optimizer
        
        optimizer = get_startup_optimizer()
        
        # Test phase timing
        optimizer.start_phase("test_phase")
        time.sleep(0.1)  # Simulate work
        duration = optimizer.end_phase("test_phase")
        
        if 0.05 <= duration <= 0.2:  # Should be around 0.1s
            logger.info(f"✅ Phase timing working correctly ({duration:.3f}s)")
        else:
            logger.warning(f"⚠️ Phase timing seems off ({duration:.3f}s)")
            return False
        
        # Test startup summary
        summary = optimizer.get_startup_summary()
        if 'total_startup_time' in summary and 'phases' in summary:
            logger.info("✅ Startup summary generation working")
            return True
        else:
            logger.warning("⚠️ Startup summary incomplete")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error testing startup optimizer: {e}")
        return False

def test_application_bootstrap():
    """Test that application bootstrap works without heavy initialization"""
    logger = logging.getLogger(__name__)
    logger.info("🏗️ Testing application bootstrap performance...")
    
    try:
        # Test that we can import the bootstrapper quickly
        start_time = time.time()
        from knowledge_app.core.application_bootstrapper import ApplicationBootstrapper
        import_time = time.time() - start_time
        
        if import_time > 2.0:
            logger.warning(f"⚠️ Bootstrapper import took {import_time:.2f}s (should be < 2s)")
            return False
        
        logger.info(f"✅ Bootstrapper imported in {import_time:.3f}s")
        
        # Test bootstrapper creation (should not trigger heavy initialization)
        start_time = time.time()
        bootstrapper = ApplicationBootstrapper()
        creation_time = time.time() - start_time
        
        if creation_time > 1.0:
            logger.warning(f"⚠️ Bootstrapper creation took {creation_time:.2f}s (should be < 1s)")
            return False
        
        logger.info(f"✅ Bootstrapper created in {creation_time:.3f}s")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error testing application bootstrap: {e}")
        return False

def main():
    """Run all startup fix tests"""
    logger = setup_test_logging()
    logger.info("🧪 Starting Knowledge App startup fixes test...")
    
    tests = [
        ("Warning Suppression", test_warning_suppression),
        ("Dependency Health Checker", test_dependency_health_checker),
        ("Lazy RAG Loading", test_lazy_rag_loading),
        ("Startup Optimizer", test_startup_optimizer),
        ("Application Bootstrap", test_application_bootstrap)
    ]
    
    results = {}
    total_start_time = time.time()
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"Running test: {test_name}")
        logger.info(f"{'='*50}")
        
        try:
            start_time = time.time()
            result = test_func()
            duration = time.time() - start_time
            
            results[test_name] = {
                'success': result,
                'duration': duration
            }
            
            status = "✅ PASSED" if result else "❌ FAILED"
            logger.info(f"{status} - {test_name} ({duration:.3f}s)")
            
        except Exception as e:
            results[test_name] = {
                'success': False,
                'duration': 0,
                'error': str(e)
            }
            logger.error(f"❌ FAILED - {test_name}: {e}")
    
    # Summary
    total_duration = time.time() - total_start_time
    passed = sum(1 for r in results.values() if r['success'])
    total = len(results)
    
    logger.info(f"\n{'='*60}")
    logger.info(f"STARTUP FIXES TEST SUMMARY")
    logger.info(f"{'='*60}")
    logger.info(f"Total tests: {total}")
    logger.info(f"Passed: {passed}")
    logger.info(f"Failed: {total - passed}")
    logger.info(f"Total time: {total_duration:.3f}s")
    
    if passed == total:
        logger.info("🎉 ALL STARTUP FIXES WORKING CORRECTLY!")
        return 0
    else:
        logger.error("❌ Some startup fixes need attention")
        return 1

if __name__ == "__main__":
    sys.exit(main())
