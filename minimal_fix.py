#!/usr/bin/env python3
"""
MINIMAL FIX - Get llama-cpp-python working ASAP
Focus on getting the Knowledge App working, optimize later.
"""

import os
import sys
import subprocess
import shutil
import logging

logging.basicConfig(level=logging.INFO, format="%(message)s")
logger = logging.getLogger(__name__)

def run_cmd(cmd, timeout=60):
    """Run command with short timeout."""
    logger.info(f"🔧 {cmd}")
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=timeout)
        if result.returncode != 0:
            logger.error(f"❌ {result.stderr.strip()}")
            return False
        logger.info("✅ OK")
        return True
    except subprocess.TimeoutExpired:
        logger.error(f"❌ TIMEOUT")
        return False
    except Exception as e:
        logger.error(f"❌ {e}")
        return False

def clean_packages():
    """Clean corrupted packages."""
    logger.info("🧹 Cleaning...")
    
    import site
    site_packages = site.getsitepackages()[0]
    
    for item in os.listdir(site_packages):
        if item.startswith('-orch') or item.startswith('-umpy'):
            try:
                shutil.rmtree(os.path.join(site_packages, item))
                logger.info(f"✅ Removed {item}")
            except:
                pass

def install_basic_deps():
    """Install basic dependencies first."""
    logger.info("📦 Installing basics...")
    
    commands = [
        "pip install numpy",
        "pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu"
    ]
    
    for cmd in commands:
        if not run_cmd(cmd, timeout=180):
            return False
    return True

def install_llama_cpp():
    """Install llama-cpp-python - try precompiled first."""
    logger.info("🦙 Installing llama-cpp-python...")
    
    # Try precompiled first (CPU version)
    if run_cmd("pip install llama-cpp-python --only-binary=all"):
        return True
    
    # If that fails, try regular install
    if run_cmd("pip install llama-cpp-python", timeout=300):
        return True
    
    logger.error("❌ Both methods failed")
    return False

def test_basic():
    """Test basic functionality."""
    logger.info("🧪 Testing...")
    
    try:
        import torch
        logger.info(f"✅ PyTorch {torch.__version__}")
        
        from llama_cpp import Llama
        logger.info("✅ llama-cpp-python OK")
        
        return True
    except Exception as e:
        logger.error(f"❌ {e}")
        return False

def main():
    """Minimal fix approach."""
    print("🚀 MINIMAL LLAMA-CPP FIX")
    print("Getting you working ASAP...")
    print("=" * 30)
    
    clean_packages()
    
    if not install_basic_deps():
        logger.error("❌ Basic deps failed")
        return False
    
    if not install_llama_cpp():
        logger.error("❌ llama-cpp-python failed")
        return False
    
    if not test_basic():
        logger.error("❌ Test failed")
        return False
    
    print("\n🎉 BASIC SETUP COMPLETE!")
    print("✅ llama-cpp-python installed")
    print("✅ Knowledge App should work now")
    print("\n💡 Note: Using CPU version for now")
    print("   You can upgrade to CUDA later when you have time")
    
    return True

if __name__ == "__main__":
    if not main():
        print("\n❌ FAILED")
        sys.exit(1)
