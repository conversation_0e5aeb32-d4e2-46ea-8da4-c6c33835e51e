#!/usr/bin/env python3
"""
BULLETPROOF SOLUTION TEST

This script tests the final, definitive solution that eliminates ALL crash-prone components:

✅ STABLE COMPONENTS (KEPT):
- BulletproofRAGEngine (retrieval-only, no FARMReader)
- GGUFModelInference (stable llama-cpp-python)
- OfflineMCQGenerator (GGUF-only)

❌ UNSTABLE COMPONENTS (REMOVED):
- LocalModelInference (transformers + bitsandbytes crashes)
- FARMReader (RoBERTa model loading crashes)
- ExtractiveQAPipeline (heavy transformer dependencies)

This test verifies that the application can now run the quiz workflow
without any crashes on consumer hardware.
"""

import sys
import time
import logging
import warnings
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Suppress all startup warnings immediately
warnings.filterwarnings("ignore", category=UserWarning)
warnings.filterwarnings("ignore", category=DeprecationWarning)
warnings.filterwarnings("ignore", category=FutureWarning)

def setup_logging():
    """Setup logging for the test"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('bulletproof_test.log')
        ]
    )
    return logging.getLogger(__name__)

def test_bulletproof_rag_engine():
    """Test the bulletproof RAG engine (retrieval-only)"""
    logger = logging.getLogger(__name__)
    logger.info("🛡️ Testing BulletproofRAGEngine...")
    
    try:
        from knowledge_app.rag_engine import BulletproofRAGEngine
        
        # Test engine creation (should be instant)
        start_time = time.time()
        rag_engine = BulletproofRAGEngine()
        creation_time = time.time() - start_time
        
        if creation_time > 1.0:
            logger.warning(f"⚠️ RAG engine creation took {creation_time:.2f}s (should be < 1s)")
            return False
        
        logger.info(f"✅ RAG engine created in {creation_time:.3f}s")
        
        # Test that it doesn't crash on retrieval attempt (even with no documents)
        try:
            context = rag_engine.retrieve_context("test query", top_k=3)
            logger.info(f"✅ Retrieval test completed (returned {len(context)} chunks)")
            return True
        except Exception as e:
            logger.error(f"❌ Retrieval test failed: {e}")
            return False
            
    except Exception as e:
        logger.error(f"❌ BulletproofRAGEngine test failed: {e}")
        return False

def test_gguf_model_inference():
    """Test the GGUF model inference engine"""
    logger = logging.getLogger(__name__)
    logger.info("🔧 Testing GGUFModelInference...")
    
    try:
        from knowledge_app.core.gguf_model_inference import GGUFModelInference
        
        # Test engine creation (should not crash)
        model_path = "models/gguf_models/MiMo-7B-RL-Q8_0.gguf"
        gguf_engine = GGUFModelInference(model_path)
        
        logger.info("✅ GGUF engine created successfully")
        
        # Note: We don't test model loading here since the model file might not exist
        # The important thing is that the engine can be created without crashing
        return True
        
    except Exception as e:
        logger.error(f"❌ GGUFModelInference test failed: {e}")
        return False

def test_offline_mcq_generator():
    """Test the offline MCQ generator (GGUF-only)"""
    logger = logging.getLogger(__name__)
    logger.info("📝 Testing OfflineMCQGenerator...")
    
    try:
        from knowledge_app.core.offline_mcq_generator import OfflineMCQGenerator
        
        # Test generator creation
        mcq_generator = OfflineMCQGenerator()
        
        logger.info("✅ OfflineMCQGenerator created successfully")
        
        # Test that it doesn't reference any unstable components
        if hasattr(mcq_generator, 'transformers_engine'):
            logger.error("❌ CRITICAL: OfflineMCQGenerator still has transformers_engine!")
            return False
        
        if hasattr(mcq_generator, 'local_inference'):
            logger.error("❌ CRITICAL: OfflineMCQGenerator still has local_inference!")
            return False
        
        logger.info("✅ No unstable components found in OfflineMCQGenerator")
        return True
        
    except Exception as e:
        logger.error(f"❌ OfflineMCQGenerator test failed: {e}")
        return False

def test_no_unstable_imports():
    """Test that unstable modules cannot be imported"""
    logger = logging.getLogger(__name__)
    logger.info("🚫 Testing that unstable components are removed...")
    
    unstable_modules = [
        'knowledge_app.core.local_model_inference',
        'knowledge_app.core.local_question_generator',
        'knowledge_app.core.mcq_loading_thread',
        'knowledge_app.core.model_singleton'
    ]
    
    removed_count = 0
    for module_name in unstable_modules:
        try:
            __import__(module_name)
            logger.error(f"❌ CRITICAL: Unstable module {module_name} still exists!")
            return False
        except ImportError:
            logger.info(f"✅ Unstable module {module_name} properly removed")
            removed_count += 1
    
    logger.info(f"✅ All {removed_count} unstable modules properly removed")
    return True

def test_mcq_manager_stability():
    """Test that MCQ manager only uses stable components"""
    logger = logging.getLogger(__name__)
    logger.info("🎯 Testing MCQManager stability...")
    
    try:
        from knowledge_app.core.mcq_manager import MCQManager
        
        # Test manager creation
        mcq_manager = MCQManager()
        
        logger.info("✅ MCQManager created successfully")
        
        # Test offline availability check (should not crash)
        try:
            is_available = mcq_manager.is_offline_available()
            logger.info(f"✅ Offline availability check completed: {is_available}")
            return True
        except Exception as e:
            logger.error(f"❌ Offline availability check failed: {e}")
            return False
            
    except Exception as e:
        logger.error(f"❌ MCQManager test failed: {e}")
        return False

def test_application_startup():
    """Test that the application can start without crashes"""
    logger = logging.getLogger(__name__)
    logger.info("🚀 Testing application startup stability...")
    
    try:
        # Test that we can import main components without crashes
        start_time = time.time()
        
        from knowledge_app.core.application_bootstrapper import ApplicationBootstrapper
        from knowledge_app.ui.main_window import MainWindow
        
        import_time = time.time() - start_time
        
        if import_time > 5.0:
            logger.warning(f"⚠️ Application imports took {import_time:.2f}s (should be < 5s)")
            return False
        
        logger.info(f"✅ Application components imported in {import_time:.3f}s")
        
        # Test bootstrapper creation (should not trigger heavy initialization)
        bootstrapper = ApplicationBootstrapper()
        logger.info("✅ ApplicationBootstrapper created successfully")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Application startup test failed: {e}")
        return False

def main():
    """Run all bulletproof solution tests"""
    logger = setup_logging()
    logger.info("🛡️ BULLETPROOF SOLUTION TEST - FINAL STABILITY VERIFICATION")
    logger.info("=" * 70)
    
    tests = [
        ("Bulletproof RAG Engine", test_bulletproof_rag_engine),
        ("GGUF Model Inference", test_gguf_model_inference),
        ("Offline MCQ Generator", test_offline_mcq_generator),
        ("No Unstable Imports", test_no_unstable_imports),
        ("MCQ Manager Stability", test_mcq_manager_stability),
        ("Application Startup", test_application_startup)
    ]
    
    results = {}
    total_start_time = time.time()
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"🧪 Running: {test_name}")
        logger.info(f"{'='*50}")
        
        try:
            start_time = time.time()
            result = test_func()
            duration = time.time() - start_time
            
            results[test_name] = {
                'success': result,
                'duration': duration
            }
            
            status = "✅ PASSED" if result else "❌ FAILED"
            logger.info(f"{status} - {test_name} ({duration:.3f}s)")
            
        except Exception as e:
            results[test_name] = {
                'success': False,
                'duration': 0,
                'error': str(e)
            }
            logger.error(f"❌ FAILED - {test_name}: {e}")
    
    # Final summary
    total_duration = time.time() - total_start_time
    passed = sum(1 for r in results.values() if r['success'])
    total = len(results)
    
    logger.info(f"\n{'='*70}")
    logger.info(f"🛡️ BULLETPROOF SOLUTION TEST SUMMARY")
    logger.info(f"{'='*70}")
    logger.info(f"Total tests: {total}")
    logger.info(f"Passed: {passed}")
    logger.info(f"Failed: {total - passed}")
    logger.info(f"Total time: {total_duration:.3f}s")
    
    if passed == total:
        logger.info("🎉 BULLETPROOF SOLUTION VERIFIED - NO MORE CRASHES!")
        logger.info("🚀 The application is now stable for consumer hardware!")
        return 0
    else:
        logger.error("❌ Some stability issues remain - check failed tests")
        return 1

if __name__ == "__main__":
    sys.exit(main())
