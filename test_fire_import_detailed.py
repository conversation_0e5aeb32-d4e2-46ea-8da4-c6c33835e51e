#!/usr/bin/env python3
"""
Detailed Fire Estimator Import Test

This script tests exactly what's happening when we import fire_estimator.
"""

import sys
import os

# Add src directory to path
src_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'src')
if src_dir not in sys.path:
    sys.path.insert(0, src_dir)

def test_step_by_step():
    """Test step by step what imports torch"""
    
    heavy_modules = ['torch', 'transformers', 'peft', 'datasets']
    
    print("🔍 Testing step-by-step fire_estimator import...")
    
    # Step 1: Test basic imports
    print("\n📦 Step 1: Testing basic imports...")
    before = [m for m in heavy_modules if m in sys.modules]
    print(f"Heavy modules before: {before}")
    
    import logging
    import time
    import json
    import threading
    from pathlib import Path
    from dataclasses import dataclass, field
    from typing import Dict, List, Any, Optional, Callable, Tuple
    from collections import deque
    from concurrent.futures import ThreadPoolExecutor
    
    after = [m for m in heavy_modules if m in sys.modules]
    newly_imported = [m for m in after if m not in before]
    print(f"Heavy modules after basic imports: {after}")
    if newly_imported:
        print(f"⚠️ Basic imports imported: {newly_imported}")
    
    # Step 2: Test numpy import
    print("\n📦 Step 2: Testing numpy import...")
    before = [m for m in heavy_modules if m in sys.modules]
    print(f"Heavy modules before numpy: {before}")
    
    import numpy as np
    
    after = [m for m in heavy_modules if m in sys.modules]
    newly_imported = [m for m in after if m not in before]
    print(f"Heavy modules after numpy: {after}")
    if newly_imported:
        print(f"⚠️ NUMPY imported: {newly_imported}")
        return "numpy", newly_imported
    
    # Step 3: Test training_metrics import
    print("\n📦 Step 3: Testing training_metrics import...")
    before = [m for m in heavy_modules if m in sys.modules]
    print(f"Heavy modules before training_metrics: {before}")
    
    from knowledge_app.core.training_metrics import TrainingMetrics
    
    after = [m for m in heavy_modules if m in sys.modules]
    newly_imported = [m for m in after if m not in before]
    print(f"Heavy modules after training_metrics: {after}")
    if newly_imported:
        print(f"⚠️ TRAINING_METRICS imported: {newly_imported}")
        return "training_metrics", newly_imported
    
    # Step 4: Test fire_estimator classes
    print("\n📦 Step 4: Testing fire_estimator classes...")
    before = [m for m in heavy_modules if m in sys.modules]
    print(f"Heavy modules before fire_estimator classes: {before}")
    
    # Import the classes one by one
    try:
        print("  Testing ProbabilisticEstimate...")
        from knowledge_app.core.fire_estimator import ProbabilisticEstimate
        
        after = [m for m in heavy_modules if m in sys.modules]
        newly_imported = [m for m in after if m not in before]
        if newly_imported:
            print(f"⚠️ ProbabilisticEstimate imported: {newly_imported}")
            return "ProbabilisticEstimate", newly_imported
        
        print("  Testing HardwareProfile...")
        from knowledge_app.core.fire_estimator import HardwareProfile
        
        after = [m for m in heavy_modules if m in sys.modules]
        newly_imported = [m for m in after if m not in before]
        if newly_imported:
            print(f"⚠️ HardwareProfile imported: {newly_imported}")
            return "HardwareProfile", newly_imported
        
        print("  Testing OracleEngine...")
        from knowledge_app.core.fire_estimator import OracleEngine
        
        after = [m for m in heavy_modules if m in sys.modules]
        newly_imported = [m for m in after if m not in before]
        if newly_imported:
            print(f"⚠️ OracleEngine imported: {newly_imported}")
            return "OracleEngine", newly_imported
        
        print("  Testing FIREEstimator...")
        from knowledge_app.core.fire_estimator import FIREEstimator
        
        after = [m for m in heavy_modules if m in sys.modules]
        newly_imported = [m for m in after if m not in before]
        if newly_imported:
            print(f"⚠️ FIREEstimator imported: {newly_imported}")
            return "FIREEstimator", newly_imported
            
    except Exception as e:
        print(f"❌ Error importing fire_estimator classes: {e}")
        return "error", []
    
    print("✅ All imports completed without importing heavy modules")
    return None, []

def main():
    """Run the detailed test"""
    print("🚀 Starting detailed fire estimator import test...")
    print("=" * 60)
    
    culprit, imported = test_step_by_step()
    
    print("\n" + "=" * 60)
    print("📊 DETAILED RESULTS")
    print("=" * 60)
    
    if culprit:
        print(f"❌ CULPRIT FOUND: {culprit}")
        print(f"❌ Imported heavy modules: {imported}")
    else:
        print("✅ No culprit found - fire_estimator is clean!")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
