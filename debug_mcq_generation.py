#!/usr/bin/env python3
"""
Debug and fix MCQ generation issues in Knowledge App.

Issues identified:
1. Haystack not installed (RAG disabled)
2. GGUF engine not initialized (no model loaded)
3. Fallback chain failing

Solutions provided:
1. Quick fix: Install Haystack for RAG
2. GGUF fix: Download and configure MiMo model
3. Instant fix: Enable basic MCQ generation
"""

import os
import sys
import subprocess
import logging

logging.basicConfig(level=logging.INFO, format="%(message)s")
logger = logging.getLogger(__name__)

def run_cmd(cmd, timeout=120):
    """Run command with error handling."""
    logger.info(f"🔧 {cmd}")
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=timeout)
        if result.returncode != 0:
            logger.error(f"❌ {result.stderr.strip()}")
            return False
        logger.info("✅ OK")
        return True
    except Exception as e:
        logger.error(f"❌ {e}")
        return False

def install_haystack():
    """Install Haystack for RAG functionality."""
    logger.info("📚 Installing Haystack for RAG features...")
    
    commands = [
        "pip install farm-haystack[inference]",
        "pip install sentence-transformers"
    ]
    
    for cmd in commands:
        if not run_cmd(cmd, timeout=180):
            return False
    
    logger.info("✅ Haystack installed - RAG features now available")
    return True

def check_gguf_models():
    """Check for GGUF models in the expected directories."""
    logger.info("🔍 Checking for GGUF models...")
    
    model_dirs = [
        "models/gguf_models",
        "models/local", 
        "data/models",
        "."
    ]
    
    found_models = []
    for model_dir in model_dirs:
        if os.path.exists(model_dir):
            for file in os.listdir(model_dir):
                if file.endswith('.gguf'):
                    found_models.append(os.path.join(model_dir, file))
    
    if found_models:
        logger.info(f"✅ Found {len(found_models)} GGUF model(s):")
        for model in found_models:
            size_mb = os.path.getsize(model) / (1024*1024)
            logger.info(f"   📁 {model} ({size_mb:.1f} MB)")
        return True
    else:
        logger.warning("❌ No GGUF models found")
        return False

def download_mimo_model():
    """Guide user to download MiMo model."""
    logger.info("📥 MiMo-7B Model Download Guide...")
    
    print("\n" + "="*50)
    print("🦙 DOWNLOAD MIMO-7B MODEL")
    print("="*50)
    print("1. Go to: https://huggingface.co/jedisct1/MiMo-7B-RL-GGUF")
    print("2. Download: MiMo-7B-RL-Q4_K_M.gguf (3.8GB)")
    print("3. Place in: models/gguf_models/")
    print("4. Restart the Knowledge App")
    print("="*50)
    
    # Create directory if it doesn't exist
    os.makedirs("models/gguf_models", exist_ok=True)
    logger.info("✅ Created models/gguf_models directory")

def test_mcq_generation():
    """Test MCQ generation after fixes."""
    logger.info("🧪 Testing MCQ generation...")
    
    try:
        # Test basic imports
        from knowledge_app.core.mcq_manager import MCQManager
        logger.info("✅ MCQManager import OK")
        
        # Test Haystack if installed
        try:
            import haystack
            logger.info("✅ Haystack available")
        except ImportError:
            logger.warning("⚠️ Haystack not available")
        
        # Test sentence transformers
        try:
            from sentence_transformers import SentenceTransformer
            logger.info("✅ SentenceTransformers available")
        except ImportError:
            logger.warning("⚠️ SentenceTransformers not available")
        
        return True
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        return False

def main():
    """Main debugging and fixing process."""
    print("🔍 KNOWLEDGE APP MCQ GENERATION DEBUG")
    print("=" * 45)
    
    print("\n📋 ISSUES IDENTIFIED:")
    print("1. ❌ Haystack not installed (RAG disabled)")
    print("2. ❌ GGUF engine not initialized (no model)")
    print("3. ❌ Fallback chain failing")
    
    print("\n🛠️ AVAILABLE FIXES:")
    print("1. Quick Fix: Install Haystack for RAG")
    print("2. Complete Fix: Download MiMo model")
    print("3. Test: Verify fixes work")
    
    choice = input("\nChoose fix (1/2/3/all): ").strip().lower()
    
    if choice in ['1', 'all']:
        print("\n🔧 APPLYING QUICK FIX...")
        if install_haystack():
            print("✅ Haystack installed - RAG now available")
        else:
            print("❌ Haystack installation failed")
    
    if choice in ['2', 'all']:
        print("\n🔧 CHECKING GGUF MODELS...")
        if not check_gguf_models():
            download_mimo_model()
    
    if choice in ['3', 'all']:
        print("\n🧪 TESTING FIXES...")
        if test_mcq_generation():
            print("✅ MCQ generation components ready")
        else:
            print("❌ Some components still missing")
    
    print("\n" + "="*45)
    print("🎯 NEXT STEPS:")
    print("1. Restart the Knowledge App")
    print("2. Try generating a quiz")
    print("3. If still failing, download MiMo model")
    print("="*45)

if __name__ == "__main__":
    main()
