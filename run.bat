@echo off
REM Knowledge App Launcher for Windows
REM This script sets up the environment properly before launching the application

echo Starting Knowledge App...

REM Set CUDA environment variables BEFORE Python starts
set CUDA_VISIBLE_DEVICES=0
set PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:256,expandable_segments:True
set CUDA_LAUNCH_BLOCKING=0

REM Set PyQt environment variables
set QT_AUTO_SCREEN_SCALE_FACTOR=1
set QT_ENABLE_HIGHDPI_SCALING=1

REM Set Python environment variables
set PYTHONPATH=%~dp0src;%PYTHONPATH%
set PYTHONUNBUFFERED=1

REM Memory optimization
set PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:256

REM Launch the application
echo Environment configured. Launching application...
python main.py %*

REM Pause if there was an error
if %ERRORLEVEL% NEQ 0 (
    echo.
    echo Application exited with error code %ERRORLEVEL%
    pause
)
