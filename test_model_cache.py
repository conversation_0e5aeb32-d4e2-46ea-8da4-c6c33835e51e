#!/usr/bin/env python3
"""
Quick test to verify Mistral 7B model is accessible from cache
"""

import sys
import os
import time

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_model_cache():
    """Test if the model can be loaded from HuggingFace cache"""
    print("🔍 Testing Mistral 7B model cache access...")
    
    try:
        # Test HuggingFace cache access
        from transformers import AutoTokenizer
        
        model_id = "mistralai/Mistral-7B-Instruct-v0.2"
        
        print(f"📥 Loading tokenizer for {model_id}...")
        start_time = time.time()
        
        tokenizer = AutoTokenizer.from_pretrained(model_id)
        
        load_time = time.time() - start_time
        print(f"✅ Tokenizer loaded in {load_time:.2f} seconds")
        
        # Test tokenization
        test_text = "Hello, this is a test."
        tokens = tokenizer.encode(test_text)
        decoded = tokenizer.decode(tokens)
        
        print(f"🔤 Test tokenization:")
        print(f"   Input: {test_text}")
        print(f"   Tokens: {len(tokens)} tokens")
        print(f"   Decoded: {decoded}")
        
        print("✅ Model cache is working correctly!")
        print("✅ Your 13.5GB Mistral model is ready to use!")
        
        return True
        
    except Exception as e:
        print(f"❌ Error accessing model cache: {e}")
        return False

def check_cache_location():
    """Check the HuggingFace cache location"""
    print("\n🗂️ HuggingFace Cache Information:")
    
    try:
        from huggingface_hub import HfFolder
        cache_dir = os.path.expanduser("~/.cache/huggingface")
        
        print(f"Cache directory: {cache_dir}")
        
        mistral_path = os.path.join(cache_dir, "hub", "models--mistralai--Mistral-7B-Instruct-v0.2")
        if os.path.exists(mistral_path):
            print(f"✅ Mistral model found at: {mistral_path}")
            
            # Check blob sizes
            blobs_path = os.path.join(mistral_path, "blobs")
            if os.path.exists(blobs_path):
                blob_files = [f for f in os.listdir(blobs_path) if os.path.getsize(os.path.join(blobs_path, f)) > 1000000]
                total_size = sum(os.path.getsize(os.path.join(blobs_path, f)) for f in blob_files)
                print(f"✅ Found {len(blob_files)} model blob files")
                print(f"✅ Total model size: {total_size / (1024**3):.2f} GB")
        else:
            print(f"❌ Mistral model not found at expected location")
            
    except Exception as e:
        print(f"⚠️ Error checking cache: {e}")

if __name__ == "__main__":
    print("🚀 MISTRAL 7B MODEL CACHE TEST")
    print("="*50)
    
    check_cache_location()
    print()
    success = test_model_cache()
    
    print("\n" + "="*50)
    if success:
        print("🎉 SUCCESS! Your model cache is working perfectly!")
        print("🚀 The Model Singleton should now prevent reloading!")
    else:
        print("⚠️ There may be issues with the model cache")
    
    print("="*50)
