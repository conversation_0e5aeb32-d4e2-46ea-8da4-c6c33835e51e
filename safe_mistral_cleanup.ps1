# SAFE MISTRAL CLEANUP SCRIPT
# This script ONLY removes the incomplete Mistral-7B-v0.1 download
# It preserves all LM Studio models and your working Mistral-7B-Instruct-v0.2

Write-Host "🧹 SAFE MISTRAL CLEANUP SCRIPT" -ForegroundColor Cyan
Write-Host "==============================" -ForegroundColor Cyan
Write-Host ""

# Define the path to the incomplete model
$incompletePath = "C:\Users\<USER>\.cache\huggingface\hub\models--mistralai--Mistral-7B-v0.1"

# Check if the path exists
if (Test-Path $incompletePath) {
    Write-Host "🔍 Found incomplete Mistral-7B-v0.1 download at:" -ForegroundColor Yellow
    Write-Host "   $incompletePath" -ForegroundColor Gray
    Write-Host ""
    
    # Calculate size before deletion
    try {
        $size = (Get-ChildItem -Path $incompletePath -Recurse -ErrorAction SilentlyContinue | Measure-Object -Property Length -Sum).Sum
        $sizeGB = [math]::Round($size / 1GB, 2)
        Write-Host "📊 Size to be freed: $sizeGB GB" -ForegroundColor Green
    } catch {
        Write-Host "📊 Size calculation failed, but proceeding with cleanup" -ForegroundColor Yellow
    }
    
    Write-Host ""
    Write-Host "⚠️  WHAT WILL BE DELETED:" -ForegroundColor Red
    Write-Host "   ❌ Mistral-7B-v0.1 (incomplete download)" -ForegroundColor Red
    Write-Host ""
    Write-Host "✅ WHAT WILL BE PRESERVED:" -ForegroundColor Green
    Write-Host "   ✅ Mistral-7B-Instruct-v0.2 (your main working model)" -ForegroundColor Green
    Write-Host "   ✅ All LM Studio models (as requested)" -ForegroundColor Green
    Write-Host "   ✅ All LoRA adapters (your trained models)" -ForegroundColor Green
    Write-Host ""
    
    # Ask for confirmation
    $confirmation = Read-Host "Do you want to proceed with cleanup? (y/N)"
    
    if ($confirmation -eq 'y' -or $confirmation -eq 'Y') {
        Write-Host ""
        Write-Host "🗑️  Deleting incomplete Mistral-7B-v0.1..." -ForegroundColor Yellow
        
        try {
            Remove-Item -Path $incompletePath -Recurse -Force -ErrorAction Stop
            Write-Host "✅ Successfully deleted incomplete model!" -ForegroundColor Green
            Write-Host "💾 Freed up approximately $sizeGB GB of space" -ForegroundColor Green
        } catch {
            Write-Host "❌ Error during deletion: $($_.Exception.Message)" -ForegroundColor Red
            Write-Host "💡 You may need to run as Administrator" -ForegroundColor Yellow
        }
    } else {
        Write-Host "❌ Cleanup cancelled by user" -ForegroundColor Yellow
    }
} else {
    Write-Host "ℹ️  Incomplete Mistral-7B-v0.1 not found - nothing to clean up!" -ForegroundColor Blue
}

Write-Host ""
Write-Host "📋 FINAL STATUS:" -ForegroundColor Cyan
Write-Host "=================" -ForegroundColor Cyan

# Check what's still there
$workingModel = "C:\Users\<USER>\.cache\huggingface\hub\models--mistralai--Mistral-7B-Instruct-v0.2"
$lmStudioPath = "C:\Users\<USER>\.cache\lm-studio\models"
$loraPath = "C:\shared folder\knowledge_app\data\lora_adapters_mistral"

if (Test-Path $workingModel) {
    Write-Host "✅ Mistral-7B-Instruct-v0.2 (working model): PRESERVED" -ForegroundColor Green
} else {
    Write-Host "⚠️  Mistral-7B-Instruct-v0.2: NOT FOUND" -ForegroundColor Red
}

if (Test-Path $lmStudioPath) {
    $lmModels = Get-ChildItem -Path $lmStudioPath -Filter "*mistral*" -ErrorAction SilentlyContinue
    Write-Host "✅ LM Studio Mistral models: PRESERVED ($($lmModels.Count) models)" -ForegroundColor Green
} else {
    Write-Host "ℹ️  LM Studio path not found" -ForegroundColor Blue
}

if (Test-Path $loraPath) {
    Write-Host "✅ LoRA adapters: PRESERVED" -ForegroundColor Green
} else {
    Write-Host "ℹ️  LoRA adapters path not found" -ForegroundColor Blue
}

if (Test-Path $incompletePath) {
    Write-Host "⚠️  Incomplete Mistral-7B-v0.1: STILL EXISTS" -ForegroundColor Yellow
} else {
    Write-Host "✅ Incomplete Mistral-7B-v0.1: SUCCESSFULLY REMOVED" -ForegroundColor Green
}

Write-Host ""
Write-Host "🎉 Cleanup complete! Your system is now optimized." -ForegroundColor Cyan
Write-Host "🚀 Your main Mistral model and LM Studio models are safe!" -ForegroundColor Cyan
