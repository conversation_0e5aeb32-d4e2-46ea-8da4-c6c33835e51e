"""
Knowledge App Entry Point

This module serves as the main entry point for the Knowledge App application.
It initializes the application environment, sets up logging, creates the main
window, and starts the application event loop.
"""

# Environment variables are now set by wrapper scripts (run.bat/run.sh)
# This ensures they are set BEFORE Python interpreter starts
import os
import platform

# CRITICAL STARTUP OPTIMIZATION: Apply optimizations before any heavy imports
import sys
import warnings

# Suppress startup warnings immediately
warnings.filterwarnings("ignore", message=".*GetCoreSchemaHandler.*", category=UserWarning)
warnings.filterwarnings("ignore", message=".*pkg_resources.*deprecated.*", category=DeprecationWarning)
warnings.filterwarnings("ignore", message=".*quantulum3.*deprecated.*", category=UserWarning)

import signal
import traceback
import logging
import asyncio
from pathlib import Path
from typing import Type, Optional, Any, Dict, Union

# Check Python environment (allow virtual environments for better dependency management)
print(f"Python executable: {sys.executable}")
if '.venv' in sys.executable or 'venv' in sys.executable:
    print("Running in virtual environment (recommended for dependency isolation)")
else:
    print("Running in global Python environment")

# CRITICAL MEMORY FIX: Defer PyTorch import to reduce startup memory
# PyTorch will be imported lazily when actually needed
def _verify_pytorch_availability():
    """Verify PyTorch is available without importing it during startup"""
    try:
        import torch
        print(f"✅ PyTorch: {torch.__version__}")
        print(f"✅ CUDA Available: {torch.cuda.is_available()}")
        if not torch.cuda.is_available():
            print("❌ ERROR: PyTorch does not have CUDA support!")
            print("Please install CUDA-enabled PyTorch:")
            print("pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu128")
            return False
        return True
    except ImportError:
        print("❌ ERROR: PyTorch not found!")
        print("Please install PyTorch with CUDA support:")
        print("pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu128")
        return False

# Note: PyTorch verification will be done lazily when GPU features are first accessed

import psutil
import time

# Add the src directory to path for local imports
src_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'src')
if src_dir not in sys.path:
    sys.path.insert(0, src_dir)

# Enterprise-grade warning suppression for clean professional output
try:
    from knowledge_app.utils.warning_suppressor import suppress_enterprise_warnings
    suppress_enterprise_warnings()
    print("✅ Enterprise warning suppression activated for clean output")

    # Also suppress Qt-level warnings (CSS properties, QPainter, etc.)
    try:
        from knowledge_app.utils.qt_warning_suppressor import install_global_qt_warning_suppression
        install_global_qt_warning_suppression()
        print("✅ Qt-level warning suppression activated")
    except ImportError:
        print("⚠️ Qt warning suppressor not available")

except ImportError:
    # Fallback comprehensive warning suppression if module not available
    import warnings

    # Set environment variables for clean output
    os.environ['XFORMERS_MORE_DETAILS'] = '0'

    # Suppress all major warning categories
    warnings.filterwarnings('ignore', category=DeprecationWarning)
    warnings.filterwarnings('ignore', category=PendingDeprecationWarning)
    warnings.filterwarnings('ignore', category=FutureWarning)
    warnings.filterwarnings('ignore', category=ImportWarning)
    warnings.filterwarnings('ignore', category=ResourceWarning)
    warnings.filterwarnings('ignore', category=UserWarning)

    # PyQt5/SIP warnings
    warnings.filterwarnings('ignore', message=r'.*sipPyTypeDict.*')
    warnings.filterwarnings('ignore', message=r'.*SwigPy.*')
    warnings.filterwarnings('ignore', message=r'.*swigvarlink.*')
    warnings.filterwarnings('ignore', message=r'.*builtin type.*has no __module__ attribute.*')

    # xFormers warnings
    warnings.filterwarnings('ignore', message=r'.*xFormers.*')
    warnings.filterwarnings('ignore', message=r'.*XFORMERS.*')
    warnings.filterwarnings('ignore', message=r'.*DLL load failed.*')
    warnings.filterwarnings('ignore', message=r'.*flashattention.*')
    warnings.filterwarnings('ignore', message=r'.*_C_flashattention.*')

    # CSS Property warnings - Qt doesn't support CSS3 properties
    warnings.filterwarnings('ignore', message=r'.*Unknown CSS property.*')
    warnings.filterwarnings('ignore', message=r'.*CSS property.*not supported.*')
    warnings.filterwarnings('ignore', message=r'.*box-shadow.*')
    warnings.filterwarnings('ignore', message=r'.*text-shadow.*')
    warnings.filterwarnings('ignore', message=r'.*transform.*')
    warnings.filterwarnings('ignore', message=r'.*transition.*')
    warnings.filterwarnings('ignore', message=r'.*animation.*')
    warnings.filterwarnings('ignore', message=r'.*filter.*')
    warnings.filterwarnings('ignore', message=r'.*backdrop-filter.*')

    # QPainter warnings from animations
    warnings.filterwarnings('ignore', message=r'.*QPainter.*')
    warnings.filterwarnings('ignore', message=r'.*painter.*not active.*')
    warnings.filterwarnings('ignore', message=r'.*QPaintDevice.*')

    print("✅ Comprehensive warning suppression activated (including CSS and QPainter warnings)")

# CRITICAL MEMORY FIX: Defer PyTorch configuration to reduce startup memory
def _configure_pytorch():
    """Configure PyTorch settings when first needed"""
    try:
        import torch

        # Configure PyTorch settings with enterprise-grade GPU memory optimization
        torch.backends.cudnn.benchmark = True  # Enable cuDNN auto-tuner
        torch.backends.cudnn.deterministic = False  # Allow non-deterministic ops for better performance
        torch.set_float32_matmul_precision('high')  # Use high precision matrix multiplication
        torch.set_default_dtype(torch.float32)  # Use float32 by default

        # Enterprise GPU Memory Management: Enable PyTorch caching allocator for optimized memory management
        # This prevents GPU memory fragmentation and improves stability during long training sessions
        os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'max_split_size_mb:128'
        print("✅ PyTorch caching allocator configured for enterprise-grade memory management")

        if torch.cuda.is_available():
            torch.set_default_device('cuda')  # Use CUDA by default if available
            torch.amp.autocast('cuda', enabled=True)  # Enable automatic mixed precision
            print(f"✅ CUDA device configured: {torch.cuda.get_device_name()}")
            print(f"✅ GPU memory optimization enabled (max_split_size: 128MB)")

        return True
    except Exception as e:
        print(f"❌ Failed to configure PyTorch: {e}")
        return False

# Note: PyTorch configuration will be done lazily when GPU features are first accessed

# CRITICAL MEMORY FIX: Import only lightweight modules during startup
# Heavy modules will be imported lazily when needed
from knowledge_app.core.config_manager import ConfigManager, get_config
from knowledge_app.utils.error_handler import ErrorHandler, ErrorCategory, ErrorSeverity
from knowledge_app.utils.shutdown_manager import ShutdownManager
from knowledge_app.utils.resource_manager import ResourceManager, ResourceType, ResourcePriority
from knowledge_app.utils.logging_setup import setup_logging
from knowledge_app.utils.dependency_manager import check_dependencies

# Note: Heavy modules like ModelManager, GPUManager, etc. will be imported lazily

from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import QTimer

# Initialize logging
logger = logging.getLogger(__name__)

# Initialize Pydantic configuration for NumPy arrays
try:
    from knowledge_app.core.pydantic_config import configure_pydantic_for_dataframes
    configure_pydantic_for_dataframes()
    logger.info("✅ Pydantic configured for NumPy arrays and DataFrames")
except Exception as e:
    logger.warning(f"⚠️ Could not configure Pydantic: {e}")

class ApplicationManager:
    """Manages the Knowledge App application lifecycle"""
    
    def __init__(self):
        self.app = None
        self.window = None
        self.splash = None
        self.config = None
        self.error_handler = None
        self.shutdown_manager = None
        self.resource_manager = None
        self.memory_manager = None
        self.storage_manager = None
        self.gpu_manager = None
        self.model_manager = None
        self.image_manager = None
        self._crash_recovery_interval = 300  # 5 minutes
        self._last_state_save = 0
        
    def _setup_signal_handlers(self):
        """Set up signal handlers for graceful shutdown"""
        def signal_handler(signum, frame):
            logger.info(f"Received signal {signum}")
            self.cleanup()
            sys.exit(0)
            
        # Register signal handlers
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
    def _save_application_state(self):
        """Save current application state for crash recovery"""
        try:
            current_time = time.time()
            if current_time - self._last_state_save >= self._crash_recovery_interval:
                if self.config:
                    self.config.save_config()
                if self.window:
                    self.window.save_state()
                self._last_state_save = current_time
                logger.debug("Application state saved")
        except Exception as e:
            logger.error(f"Failed to save application state: {e}")
            
    def _setup_memory_monitoring(self):
        """Set up memory monitoring and reporting"""
        def log_memory_usage():
            process = psutil.Process()
            memory_info = process.memory_info()
            memory_percent = process.memory_percent()
            
            logger.info(f"Memory usage: {memory_info.rss / 1024 / 1024:.1f}MB ({memory_percent:.1f}%)")
            
            if torch.cuda.is_available():
                for i in range(torch.cuda.device_count()):
                    gpu_memory = torch.cuda.memory_allocated(i) / 1024 / 1024
                    gpu_memory_percent = gpu_memory / torch.cuda.get_device_properties(i).total_memory * 100
                    logger.info(f"GPU {i} memory: {gpu_memory:.1f}MB ({gpu_memory_percent:.1f}%)")
                    
        # Schedule periodic memory usage logging
        if self.app:
            timer = QTimer()
            timer.timeout.connect(log_memory_usage)
            timer.start(60000)  # Every minute
            
    def initialize_logging(self):
        """Initialize logging system"""
        try:
            setup_logging()
            logger.info("Logging system initialized")
        except Exception as e:
            print(f"Failed to initialize logging: {e}", file=sys.stderr)
            raise
            
    def initialize_managers(self):
        """Initialize lightweight managers only - defer heavy ones"""
        try:
            # Initialize error handler first
            self.error_handler = ErrorHandler()
            self.error_handler.initialize(Path("logs"))

            # Initialize shutdown manager and setup signal handlers
            self.shutdown_manager = ShutdownManager()
            self._setup_signal_handlers()

            # Initialize resource manager
            self.resource_manager = ResourceManager()

            # Load configuration
            self.config = get_config()
            if not self.config:
                raise RuntimeError("Failed to initialize configuration")

            # CRITICAL MEMORY FIX: Defer heavy manager initialization
            # These will be initialized lazily when first needed
            self.memory_manager = None
            self.storage_manager = None
            self.gpu_manager = None
            self.model_manager = None
            self.image_manager = None

            # Track initialization state
            self._managers_initialized = {
                'memory': False,
                'storage': False,
                'gpu': False,
                'model': False,
                'image': False
            }
            
            # Register cleanup handlers
            self._register_cleanup_handlers()
            
            # Setup memory monitoring
            self._setup_memory_monitoring()
            
            logger.info("All managers initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize managers: {e}")
            raise

    def _initialize_memory_manager_if_needed(self):
        """Lazily initialize memory manager when needed"""
        if not self._managers_initialized['memory']:
            try:
                logger.info("🔄 Initializing memory manager (lazy initialization)...")
                from knowledge_app.core.memory_manager import MemoryManager

                memory_config = {
                    'memory_threshold': self.config.get_value('memory_threshold', 0.85),
                    'memory_cache_path': 'data/memory_cache',
                    'memory_cache_size': 128 * 1024 * 1024  # 128MB
                }
                self.memory_manager = MemoryManager(memory_config)
                self._managers_initialized['memory'] = True
                logger.info("✅ Memory manager initialized successfully")
            except Exception as e:
                logger.error(f"❌ Failed to initialize memory manager: {e}")
                self.memory_manager = None

    def _initialize_storage_manager_if_needed(self):
        """Lazily initialize storage manager when needed"""
        if not self._managers_initialized['storage']:
            try:
                logger.info("🔄 Initializing storage manager (lazy initialization)...")
                from knowledge_app.core.storage_manager import StorageManager

                storage_config = {
                    'data_path': 'data',
                    'max_cache_size': 1 * 1024 * 1024 * 1024,  # 1GB
                    'cleanup_threshold': 0.85
                }
                self.storage_manager = StorageManager(storage_config)
                self._managers_initialized['storage'] = True
                logger.info("✅ Storage manager initialized successfully")
            except Exception as e:
                logger.error(f"❌ Failed to initialize storage manager: {e}")
                self.storage_manager = None

    def _initialize_gpu_manager_if_needed(self):
        """Lazily initialize GPU manager when needed"""
        if not self._managers_initialized['gpu']:
            try:
                # First verify PyTorch is available
                if not _verify_pytorch_availability():
                    logger.warning("⚠️ PyTorch not available, skipping GPU manager")
                    return

                # Configure PyTorch
                _configure_pytorch()

                logger.info("🔄 Initializing GPU manager (lazy initialization)...")
                from knowledge_app.core.gpu_manager import GPUManager
                import torch

                if torch.cuda.is_available():
                    self.gpu_manager = GPUManager()
                    self.gpu_manager.initialize()
                    self._managers_initialized['gpu'] = True
                    logger.info("✅ GPU manager initialized successfully")
                else:
                    logger.warning("⚠️ CUDA not available, GPU manager not initialized")
            except Exception as e:
                logger.error(f"❌ Failed to initialize GPU manager: {e}")
                self.gpu_manager = None

    def _initialize_model_manager_if_needed(self):
        """Lazily initialize model manager when needed"""
        if not self._managers_initialized['model']:
            try:
                logger.info("🔄 Initializing model manager (lazy initialization)...")
                from knowledge_app.core.model_manager import ModelManager

                model_config = {
                    'base_path': 'data/models',
                    'max_size': 2 * 1024 * 1024 * 1024,  # 2GB
                    'cleanup_threshold': 0.85,
                    'cache_expiry': 3600  # 1 hour
                }
                self.model_manager = ModelManager(model_config)
                self._managers_initialized['model'] = True
                logger.info("✅ Model manager initialized successfully")
            except Exception as e:
                logger.error(f"❌ Failed to initialize model manager: {e}")
                self.model_manager = None

    def _initialize_image_manager_if_needed(self):
        """Lazily initialize image manager when needed"""
        if not self._managers_initialized['image']:
            try:
                logger.info("🔄 Initializing image manager (lazy initialization)...")
                from knowledge_app.core.image_manager import ImageManager

                image_config = {
                    'base_path': 'data/image_cache',
                    'max_size': 512 * 1024 * 1024,  # 512MB
                    'cleanup_threshold': 0.85,
                    'cache_expiry': 3600  # 1 hour
                }
                self.image_manager = ImageManager(image_config)
                self._managers_initialized['image'] = True
                logger.info("✅ Image manager initialized successfully")
            except Exception as e:
                logger.error(f"❌ Failed to initialize image manager: {e}")
                self.image_manager = None
            
    def _register_cleanup_handlers(self):
        """Register cleanup handlers with shutdown manager"""
        try:
            # Register cleanup handlers with proper priority order
            # Higher priority = executed first (0-100 scale)

            # 1. Stop UI interactions first (highest priority)
            self.shutdown_manager.register_handler(
                'main_window',
                lambda: self.window.close() if self.window else None,
                category='ui',
                priority=90
            )

            # 2. Stop model operations
            if self.model_manager:
                self.shutdown_manager.register_handler(
                    'model_manager',
                    self.model_manager.cleanup,
                    category='models',
                    priority=80
                )

            # 3. Clean up GPU resources
            if self.gpu_manager:
                self.shutdown_manager.register_handler(
                    'gpu_manager',
                    self.gpu_manager.cleanup,
                    category='gpu',
                    priority=70
                )

            # 4. Clean up storage
            self.shutdown_manager.register_handler(
                'storage_manager',
                self.storage_manager.cleanup,
                category='storage',
                priority=60
            )

            # 5. Clean up memory
            self.shutdown_manager.register_handler(
                'memory_manager',
                self.memory_manager.cleanup,
                category='memory',
                priority=50
            )

            # 6. Final system cleanup (lowest priority)
            self.shutdown_manager.register_handler(
                'resource_manager',
                self.resource_manager.cleanup,
                category='system',
                priority=40
            )
            
        except Exception as e:
            logger.error(f"Failed to register cleanup handlers: {e}")
            raise
            
    def create_application(self):
        """Create and configure the QApplication instance with lazy imports"""
        try:
            # Ensure Qt warning suppression is active before creating QApplication
            try:
                from knowledge_app.utils.qt_warning_suppressor import install_global_qt_warning_suppression
                install_global_qt_warning_suppression()
                logger.debug("Qt warning suppression ensured before QApplication creation")
            except ImportError:
                logger.debug("Qt warning suppressor not available")

            self.app = QApplication(sys.argv)
            self.app.setStyle('Fusion')

            # CRITICAL MEMORY FIX: Import splash screen lazily
            from knowledge_app.ui.splash_screen import ModernSplashScreen

            # Show splash screen
            self.splash = ModernSplashScreen()
            self.splash.show()
            self.app.processEvents()

            # Register application with resource manager
            self.resource_manager.register_resource(
                'qt_application',
                self.app,
                ResourceType.UI,
                cleanup_handler=lambda: self.app.quit(),
                priority=ResourcePriority.CRITICAL
            )

        except Exception as e:
            logger.error(f"Failed to create application: {e}")
            raise
            
    def create_main_window(self):
        """Create and initialize the main window with lazy imports"""
        try:
            # CRITICAL MEMORY FIX: Import main window lazily
            from knowledge_app.ui.main_window import MainWindow

            self.window = MainWindow(self.config)

            # Register window with resource manager
            self.resource_manager.register_resource(
                'main_window',
                self.window,
                ResourceType.UI,
                cleanup_handler=lambda: self.window.close(),
                priority=ResourcePriority.HIGH
            )

        except Exception as e:
            logger.error(f"Failed to create main window: {e}")
            raise
            
    def run(self) -> int:
        """
        Run the application
        
        Returns:
            int: Application exit code
        """
        try:
            # Initialize systems
            self.initialize_logging()
            self.initialize_managers()
            
            # Create application
            self.create_application()
            
            # Check dependencies
            self.splash.update_progress(20, "Checking dependencies...")
            if not check_dependencies():
                raise RuntimeError("Critical dependencies missing")
                
            # CRITICAL MEMORY FIX: Skip all heavy initialization during startup
            self.splash.update_progress(40, "Preparing components (deferred)...")
            # Heavy managers will be initialized lazily when first needed
                
            # Create main window
            self.splash.update_progress(60, "Creating main window...")
            self.create_main_window()
            
            # Final initialization
            self.splash.update_progress(80, "Finalizing setup...")
            if self.window:
                self.window.show()
                
            # Close splash screen
            self.splash.update_progress(100, "Ready")
            self.splash.finish(self.window)
            
            # Start periodic state saving
            if self.app:
                timer = QTimer()
                timer.timeout.connect(self._save_application_state)
                timer.start(self._crash_recovery_interval * 1000)
            
            # Run event loop
            return self.app.exec_()
            
        except Exception as e:
            self.error_handler.handle_error(
                e,
                context="Application startup",
                severity=ErrorSeverity.CRITICAL,
                show_dialog=True
            )
            return 1
            
    def cleanup(self):
        """Clean up application resources"""
        try:
            if self.shutdown_manager:
                self.shutdown_manager.shutdown()
        except Exception as e:
            logger.error(f"Error during application cleanup: {e}")

def main():
    """
    Main application entry point - ENTERPRISE EDITION

    Uses professional bootstrapping with dependency injection
    instead of the old God Object pattern.
    """
    try:
        # Import the enterprise bootstrapper
        from src.knowledge_app.core.application_bootstrapper import bootstrap_application

        # Bootstrap the application using enterprise patterns
        return bootstrap_application()

    except ImportError:
        # Fallback to legacy ApplicationManager if new system not available
        logger.warning("Enterprise bootstrapper not available, falling back to legacy system")
        app_manager = ApplicationManager()
        try:
            return app_manager.run()
        finally:
            app_manager.cleanup()

    except Exception as e:
        logger.error(f"Critical application error: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
