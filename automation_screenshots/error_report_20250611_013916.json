{"timestamp": "2025-06-11T01:39:16.663069", "error_type": "launch_failure", "message": "'ConfigManager' object is not subscriptable", "stack_trace": "Traceback (most recent call last):\n  File \"C:\\shared folder\\knowledge_app\\tests\\gui_automation_debugger.py\", line 141, in _launch_application\n    self.main_window = MainWindow(config)\n  File \"C:\\shared folder\\knowledge_app\\src\\knowledge_app\\ui\\main_window.py\", line 746, in __init__\n    self.setup_managers()\n  File \"C:\\shared folder\\knowledge_app\\src\\knowledge_app\\ui\\main_window.py\", line 787, in setup_managers\n    self.model_manager = ModelManager(self.config)\n  File \"C:\\shared folder\\knowledge_app\\src\\knowledge_app\\core\\model_manager.py\", line 86, in __init__\n    super().__init__(config)\n  File \"C:\\shared folder\\knowledge_app\\src\\knowledge_app\\core\\cache_manager.py\", line 42, in __init__\n    self.base_path = Path(config['base_path'])\nTypeError: 'ConfigManager' object is not subscriptable\n", "screenshot_path": null, "ui_state": {"automation_state": "error_detected", "main_window_visible": false, "training_dialog_visible": false, "active_widgets": [{"type": "QLabel", "text": "Version 1.0.0", "enabled": true}, {"type": "QLabel", "text": "Knowledge App", "enabled": true}, {"type": "QProgressBar", "text": "0%", "enabled": true}, {"type": "QWidget", "text": "", "enabled": true}, {"type": "QLabel", "text": "Initializing...", "enabled": true}, {"type": "ModernSplashScreen", "text": "", "enabled": true}], "visible_buttons": []}, "suggested_fix": "Check if all dependencies are installed. Run 'pip install -r requirements.txt'", "automation_state": "error_detected", "retry_count": 0}