from PyQt5.QtGui import QFont

# Modern system fonts that work well across platforms
GLOBAL_FONT_FAMILY = "Segoe UI"  # Primary font - falls back to system default if not available
FALLBACK_FONTS = ["SF Pro Display", "Helvetica Neue", "Arial", "Roboto"]

# Font size scale (in pixels)
FONT_SIZES = {
    'xxs': 10,
    'xs': 12,
    'sm': 14,
    'base': 16,
    'md': 18,
    'lg': 20,
    'xl': 24,
    'xxl': 32,
    'huge': 48
}

# Font weight definitions
FONT_WEIGHTS = {
    'light': QFont.Light,
    'normal': QFont.Normal,
    'medium': QFont.Medium,
    'demibold': QFont.DemiBold,
    'bold': QFont.Bold,
    'black': QFont.Black
}

def get_font(size_key='base', weight='normal', family=None):
    """Get a QFont with specified size and weight"""
    size = FONT_SIZES.get(size_key, FONT_SIZES['base'])
    weight = FONT_WEIGHTS.get(weight, FONT_WEIGHTS['normal'])
    font_family = family or GLOBAL_FONT_FAMILY
    
    font = QFont(font_family, size)
    font.setWeight(weight)
    
    # Set fallback fonts
    font.insertSubstitutions(GLOBAL_FONT_FAMILY, FALLBACK_FONTS)
    
    return font

# Predefined font styles for common uses
TITLE_FONT = get_font('xxl', 'bold')
SUBTITLE_FONT = get_font('xl', 'medium')
HEADER_FONT = get_font('lg', 'bold')
LABEL_FONT = get_font('base', 'medium')
BUTTON_FONT = get_font('base', 'demibold')
BODY_FONT = get_font('base', 'normal')
SMALL_FONT = get_font('sm', 'normal')
TINY_FONT = get_font('xs', 'normal')
