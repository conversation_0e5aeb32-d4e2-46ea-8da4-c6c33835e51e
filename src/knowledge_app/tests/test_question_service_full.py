"""
Full integration tests for the QuestionService class.

This module contains comprehensive tests for the question generation service,
including observer pattern implementation and online/offline modes.
"""

import pytest
import logging
from unittest.mock import Mock, AsyncMock
from typing import Dict, Any, Optional

from knowledge_app.core.question_service import QuestionService
from knowledge_app.core.interfaces import QuestionObserver

# Configure logging
logger = logging.getLogger(__name__)

class DummyObserver(QuestionObserver):
    """Mock observer for testing QuestionService notifications."""
    
    def __init__(self):
        self.progress = 0
        self.question = None
        self.error = None
    
    def on_progress_update(self, progress: int) -> None:
        """Handle progress update notification."""
        self.progress = progress
        logger.debug(f"Progress updated to {progress}%")
    
    def on_question_generated(self, question: Dict[str, Any]) -> None:
        """Handle question generated notification."""
        self.question = question
        logger.debug("Question generated notification received")
        
    def on_error(self, error: str) -> None:
        """Handle error notification."""
        self.error = error
        logger.error(f"Error notification received: {error}")

@pytest.fixture
def mock_model_manager():
    """Create a mock model manager."""
    manager = AsyncMock()
    manager.generate.return_value = """Question: What is Q?
Options:
A) Option A
B) Option B
C) Option C
D) Option D

Correct Answer: A

Explanation: Test explanation"""
    return manager

@pytest.fixture
def mock_parser():
    """Create a mock question parser."""
    return Mock(return_value={
        'question': 'What is Q?',
        'options': {
            'A': 'Option A',
            'B': 'Option B',
            'C': 'Option C',
            'D': 'Option D'
        },
        'correct_answer': 'A',
        'explanation': 'Test explanation'
    })

@pytest.fixture
def mock_api():
    """Create a mock API client."""
    return AsyncMock(return_value="""Question: What is Q?
Options:
A) Option A
B) Option B
C) Option C
D) Option D

Correct Answer: A

Explanation: Test explanation""")

@pytest.fixture
def question_service(mock_model_manager, mock_parser, mock_api):
    """Create a QuestionService instance for testing."""
    return QuestionService(
        model_manager=mock_model_manager,
        parse_mistral_mcq_output=mock_parser,
        get_question_from_groq=mock_api
    )

def test_observer_notification(question_service):
    """Test that observers are properly notified of events."""
    try:
        # Set up observer
        observer = DummyObserver()
        question_service.add_observer(observer)
        
        # Test progress notification
        question_service.notify_progress(50)
        assert observer.progress == 50, "Progress should be updated to 50"
        
        # Test question notification
        question = {
            'question': 'Q',
            'options': {'A': 'Option A', 'B': 'Option B', 'C': 'Option C', 'D': 'Option D'},
            'correct_answer': 'A',
            'explanation': 'Test explanation'
        }
        question_service.notify_question_generated(question)
        assert observer.question == question, "Question should be received by observer"
        
        logger.info("Observer notification test passed successfully")
        
    except Exception as e:
        logger.error(f"Error in observer notification test: {e}")
        raise

@pytest.mark.asyncio
async def test_generate_question_offline(question_service, monkeypatch):
    """Test question generation in offline mode."""
    try:
        # Set up observer
        observer = DummyObserver()
        question_service.add_observer(observer)
        
        # Mock offline model generation
        async def mock_generate(prompt):
            return """Question: Q
Options:
A) Option A
B) Option B
C) Option C
D) Option D

Correct Answer: A

Explanation: Test explanation"""
        
        monkeypatch.setattr(question_service, '_generate_with_offline_model', mock_generate)
        
        # Generate question
        result = await question_service.generate_question(
            context='topic',
            mode='mode',
            submode='submode',
            examples=[],
            offline_mode=True,
            local_model_is_available=lambda: True,
            max_retries=1
        )
        
        # Verify result
        assert result is not None, "Result should not be None"
        assert isinstance(result, dict), "Result should be a dictionary"
        assert 'question' in result, "Result should contain question"
        assert 'options' in result, "Result should contain options"
        assert 'correct_answer' in result, "Result should contain correct answer"
        assert 'explanation' in result, "Result should contain explanation"
        
        # Verify observer notifications
        assert observer.progress > 0, "Observer should receive progress updates"
        assert observer.question is not None, "Observer should receive question"
        assert observer.error is None, "Observer should not receive error"
        
        logger.info("Offline question generation test passed successfully")
        
    except Exception as e:
        logger.error(f"Error in offline question generation test: {e}")
        raise

@pytest.mark.asyncio
async def test_generate_question_online(question_service, monkeypatch):
    """Test question generation in online mode."""
    try:
        # Set up observer
        observer = DummyObserver()
        question_service.add_observer(observer)
        
        # Mock offline model generation to ensure it's not used
        async def mock_generate(prompt):
            raise Exception('Should not be called')
        monkeypatch.setattr(question_service, '_generate_with_offline_model', mock_generate)
        
        # Generate question
        result = await question_service.generate_question(
            context='topic',
            mode='mode',
            submode='submode',
            examples=[],
            offline_mode=False,
            local_model_is_available=lambda: False,
            max_retries=1
        )
        
        # Verify result
        assert result is not None, "Result should not be None"
        assert isinstance(result, dict), "Result should be a dictionary"
        assert 'question' in result, "Result should contain question"
        assert 'options' in result, "Result should contain options"
        assert 'correct_answer' in result, "Result should contain correct answer"
        assert 'explanation' in result, "Result should contain explanation"
        
        # Verify observer notifications
        assert observer.progress > 0, "Observer should receive progress updates"
        assert observer.question is not None, "Observer should receive question"
        assert observer.error is None, "Observer should not receive error"
        
        logger.info("Online question generation test passed successfully")
        
    except Exception as e:
        logger.error(f"Error in online question generation test: {e}")
        raise

@pytest.mark.asyncio
async def test_generate_question_error_handling(question_service):
    """Test error handling during question generation."""
    try:
        # Set up observer
        observer = DummyObserver()
        question_service.add_observer(observer)
        
        # Force an error by making both online and offline modes fail
        question_service.model_manager.generate.side_effect = Exception("Model error")
        question_service.get_question_from_groq.side_effect = Exception("API error")
        
        with pytest.raises(Exception):
            await question_service.generate_question(
                context='topic',
                mode='mode',
                submode='submode',
                examples=[],
                offline_mode=True,
                local_model_is_available=lambda: True,
                max_retries=1
            )
        
        # Verify error handling
        assert observer.error is not None, "Observer should receive error notification"
        logger.info("Error handling test passed successfully")
        
    except Exception as e:
        logger.error(f"Error in error handling test: {e}")
        raise
