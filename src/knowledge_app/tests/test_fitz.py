"""
Tests for PyMuPDF (fitz) functionality.

This module tests the PDF manipulation capabilities using PyMuPDF.
"""

import os
import pytest
import logging
from pathlib import Path
import tempfile

# Configure logging
logger = logging.getLogger(__name__)

# Try to import fitz, but don't fail if it's not available
try:
    import fitz
    FITZ_AVAILABLE = True
    logger.info(f"Successfully imported fitz (PyMuPDF) version: {getattr(fitz, 'version', 'unknown')}")
except ImportError as e:
    FITZ_AVAILABLE = False
    logger.warning(f"PyMuPDF (fitz) not installed: {e}")
    logger.info("Tests requiring PyMuPDF will be skipped.")

@pytest.fixture
def temp_pdf_file():
    """Create a temporary PDF file for testing."""
    with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as temp_file:
        temp_path = Path(temp_file.name)
        yield temp_path
        # Cleanup after test
        try:
            if temp_path.exists():
                temp_path.unlink()
        except Exception as e:
            logger.warning(f"Failed to delete temp file {temp_path}: {e}")

@pytest.mark.skipif(not FITZ_AVAILABLE, reason="PyMuPDF (fitz) not installed")
def test_fitz_module():
    """Test if the fitz module is properly installed and functioning."""
    logger.info("Testing fitz module")
    
    # Check module attributes
    assert hasattr(fitz, "open"), "fitz module should have 'open' function"
    assert hasattr(fitz, "version"), "fitz module should have 'version' attribute"
    
    # Log version information
    logger.info(f"PyMuPDF version: {fitz.version}")

@pytest.mark.skipif(not FITZ_AVAILABLE, reason="PyMuPDF (fitz) not installed")
def test_fitz_create_pdf(temp_pdf_file):
    """Test if we can create a basic PDF file with PyMuPDF."""
    try:
        # Create a new PDF document
        doc = fitz.open()
        page = doc.new_page()  # Add a new page
        
        # Add some text
        text_point = fitz.Point(50, 100)  # x, y coordinates on the page
        page.insert_text(text_point, "Test text created by PyMuPDF", fontsize=12)
        
        # Save the document
        doc.save(str(temp_pdf_file))
        doc.close()
        
        # Verify the file exists
        assert temp_pdf_file.exists(), "PDF file should exist"
        assert temp_pdf_file.stat().st_size > 0, "PDF file should not be empty"
        
        # Reopen to verify content
        doc = fitz.open(str(temp_pdf_file))
        assert doc.page_count == 1, "PDF should contain exactly 1 page"
        
        # Get text from the page
        text = doc[0].get_text()
        assert "Test text created by PyMuPDF" in text, "PDF should contain the test text"
        
        doc.close()
        logger.info(f"Successfully created and verified PDF at {temp_pdf_file}")
        
    except Exception as e:
        logger.error(f"Error in PDF creation test: {e}")
        raise

@pytest.mark.skipif(not FITZ_AVAILABLE, reason="PyMuPDF (fitz) not installed")
def test_fitz_modify_pdf(temp_pdf_file):
    """Test if we can modify an existing PDF file."""
    try:
        # Create initial PDF
        doc = fitz.open()
        page = doc.new_page()
        doc.save(str(temp_pdf_file))
        doc.close()
        
        # Modify the PDF
        doc = fitz.open(str(temp_pdf_file))
        page = doc[0]
        
        # Add text
        text_point = fitz.Point(50, 100)
        page.insert_text(text_point, "Modified PDF content")
        
        # Add rectangle
        rect = fitz.Rect(50, 150, 200, 200)
        page.draw_rect(rect)
        
        # Save modifications
        doc.save(str(temp_pdf_file))
        doc.close()
        
        # Verify modifications
        doc = fitz.open(str(temp_pdf_file))
        text = doc[0].get_text()
        assert "Modified PDF content" in text, "PDF should contain the modified text"
        doc.close()
        
        logger.info(f"Successfully modified and verified PDF at {temp_pdf_file}")
        
    except Exception as e:
        logger.error(f"Error in PDF modification test: {e}")
        raise
