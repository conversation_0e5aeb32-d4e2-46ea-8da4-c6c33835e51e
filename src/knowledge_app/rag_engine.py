"""
BULLETPROOF RAG Engine - Final Stable Version

This is the DEFINITIVE, CRASH-PROOF RAG engine that eliminates ALL unstable components:

❌ REMOVED (crash-prone):
- FARMReader (RoBERTa model loading crashes)
- ExtractiveQAPipeline (unstable transformer dependencies)
- Heavy transformer model loading

✅ KEPT (stable):
- FAISSDocumentStore (lightweight vector storage)
- EmbeddingRetriever (stable sentence-transformers)
- Pure retrieval workflow (no heavy model loading)

This engine ONLY retrieves relevant text chunks. Question generation is handled
by the stable GGUF engine separately. Maximum stability guaranteed.
"""

import os
import logging
from typing import List, Dict, Any, Optional

logger = logging.getLogger(__name__)

# BULLETPROOF: Only import stable, lightweight components
try:
    import pdfplumber
    PDFPLUMBER_AVAILABLE = True
    logger.debug("✅ pdfplumber available")
except ImportError:
    pdfplumber = None
    PDFPLUMBER_AVAILABLE = False
    logger.warning("⚠️ pdfplumber not available - PDF ingestion disabled")

# Import ONLY the stable Haystack components (NO FARMReader, NO ExtractiveQAPipeline)
try:
    from haystack.document_stores import FAISSDocumentStore
    from haystack.nodes import EmbeddingRetriever
    from haystack.utils import clean_wiki_text
    HAYSTACK_AVAILABLE = True
    logger.info("✅ Stable Haystack components loaded (retrieval-only)")
except ImportError:
    FAISSDocumentStore = None
    EmbeddingRetriever = None
    clean_wiki_text = None
    HAYSTACK_AVAILABLE = False
    logger.error("❌ Haystack not available - RAG features completely disabled")


class BulletproofRAGEngine:
    """
    BULLETPROOF RAG Engine - Maximum Stability Version
    
    This engine is designed for ZERO crashes on consumer hardware:
    - NO heavy transformer models
    - NO FARMReader (RoBERTa crashes)
    - PURE retrieval only
    - Lazy initialization
    - Comprehensive error handling
    """
    
    def __init__(self, db_path="faiss_index.db", embedding_model="sentence-transformers/all-MiniLM-L6-v2"):
        self.db_path = db_path
        self.embedding_model = embedding_model
        
        # Components (initialized lazily)
        self.document_store = None
        self.retriever = None
        self.is_initialized = False
        self._initialization_error = None
        
        logger.info(f"🛡️ BulletproofRAGEngine created (db_path={db_path})")
        logger.info("🔄 Initialization deferred for maximum startup performance")
    
    def _lazy_initialize(self) -> bool:
        """
        Lazy initialization of RAG components.
        Returns True if successful, False if failed.
        """
        if self.is_initialized:
            return True
        
        if self._initialization_error:
            logger.warning(f"⚠️ Previous initialization failed: {self._initialization_error}")
            return False
        
        if not HAYSTACK_AVAILABLE:
            self._initialization_error = "Haystack not available"
            logger.error("❌ Cannot initialize RAG: Haystack not installed")
            return False
        
        try:
            logger.info("🔄 Performing lazy RAG initialization...")
            
            # Initialize document store
            self.document_store = FAISSDocumentStore(
                faiss_index_factory_str="Flat",
                sql_url=f"sqlite:///{self.db_path}.sqlite3"
            )
            logger.debug("✅ FAISS document store initialized")
            
            # Initialize retriever (stable sentence-transformers)
            self.retriever = EmbeddingRetriever(
                document_store=self.document_store,
                embedding_model=self.embedding_model,
                use_gpu=True  # Will fallback to CPU if GPU not available
            )
            logger.debug("✅ Embedding retriever initialized")
            
            # Update embeddings only if documents exist
            doc_count = self.document_store.get_document_count()
            if doc_count > 0:
                logger.info(f"🔄 Updating embeddings for {doc_count} documents...")
                self.document_store.update_embeddings(self.retriever)
                logger.info("✅ Embeddings updated successfully")
            else:
                logger.info("ℹ️ No documents in store, skipping embedding update")
            
            self.is_initialized = True
            logger.info("🎉 RAG engine initialized successfully (STABLE MODE)")
            return True
            
        except Exception as e:
            self._initialization_error = str(e)
            logger.error(f"❌ CRITICAL: RAG initialization failed: {e}")
            
            # Clean up partial initialization
            self.document_store = None
            self.retriever = None
            
            return False
    
    def retrieve_context(self, query: str, top_k: int = 3) -> List[str]:
        """
        Retrieve relevant text chunks for a query.
        
        This is the PRIMARY method of this engine - pure retrieval, no generation.
        
        Args:
            query: The search query
            top_k: Number of top documents to retrieve
            
        Returns:
            List of relevant text chunks (empty list if failed)
        """
        if not self._lazy_initialize():
            logger.warning("⚠️ RAG not initialized, returning empty context")
            return []
        
        try:
            logger.debug(f"🔍 Retrieving context for query: '{query[:50]}...'")
            
            retrieved_docs = self.retriever.retrieve(query=query, top_k=top_k)
            
            if not retrieved_docs:
                logger.warning("⚠️ No documents retrieved for query")
                return []
            
            # Extract text content from retrieved documents
            context_chunks = []
            for doc in retrieved_docs:
                if hasattr(doc, 'content') and doc.content:
                    context_chunks.append(doc.content.strip())
            
            logger.info(f"✅ Retrieved {len(context_chunks)} context chunks")
            return context_chunks
            
        except Exception as e:
            logger.error(f"❌ Error during context retrieval: {e}")
            return []
    
    def ingest_pdf(self, pdf_path: str, meta: Optional[Dict] = None) -> int:
        """
        Ingest a PDF file into the document store.
        
        Args:
            pdf_path: Path to the PDF file
            meta: Optional metadata dictionary
            
        Returns:
            Number of pages ingested (0 if failed)
        """
        if not PDFPLUMBER_AVAILABLE:
            logger.error("❌ Cannot ingest PDF: pdfplumber not available")
            return 0
        
        if not self._lazy_initialize():
            logger.error("❌ Cannot ingest PDF: RAG not initialized")
            return 0
        
        if not os.path.exists(pdf_path):
            logger.error(f"❌ PDF file not found: {pdf_path}")
            return 0
        
        try:
            logger.info(f"📄 Ingesting PDF: {pdf_path}")
            
            docs = []
            with pdfplumber.open(pdf_path) as pdf:
                for i, page in enumerate(pdf.pages):
                    text = page.extract_text() or ""
                    
                    # Clean and validate text
                    if clean_wiki_text:
                        text = clean_wiki_text(text)
                    
                    text = text.strip()
                    
                    # Only index substantial pages (at least 100 characters)
                    if len(text) >= 100:
                        doc_meta = {
                            "pdf_file": os.path.basename(pdf_path),
                            "page": i + 1,
                            **(meta or {})
                        }
                        
                        docs.append({
                            "content": text,
                            "meta": doc_meta
                        })
            
            if docs:
                # Write documents to store
                self.document_store.write_documents(docs)
                
                # Update embeddings
                logger.info(f"🔄 Updating embeddings for {len(docs)} new documents...")
                self.document_store.update_embeddings(self.retriever)
                
                logger.info(f"✅ Successfully ingested {len(docs)} pages from PDF")
                return len(docs)
            else:
                logger.warning("⚠️ No substantial content found in PDF")
                return 0
                
        except Exception as e:
            logger.error(f"❌ Error ingesting PDF: {e}")
            return 0
    
    def get_document_count(self) -> int:
        """Get the number of documents in the store."""
        if not self._lazy_initialize():
            return 0
        
        try:
            return self.document_store.get_document_count()
        except Exception as e:
            logger.error(f"❌ Error getting document count: {e}")
            return 0
    
    def cleanup(self):
        """Clean up resources."""
        logger.info("🧹 Cleaning up RAG engine resources...")
        
        try:
            if self.document_store:
                # Close any open connections
                if hasattr(self.document_store, 'close'):
                    self.document_store.close()
                
            self.document_store = None
            self.retriever = None
            self.is_initialized = False
            self._initialization_error = None
            
            # Force garbage collection
            import gc
            gc.collect()
            
            logger.info("✅ RAG engine cleanup completed")
            
        except Exception as e:
            logger.warning(f"⚠️ Error during cleanup: {e}")


# Legacy compatibility - maintain the old class name
class RAGEngine(BulletproofRAGEngine):
    """Legacy compatibility wrapper for the bulletproof RAG engine."""
    
    def __init__(self, *args, **kwargs):
        # Remove any legacy parameters that might cause issues
        kwargs.pop('lazy_init', None)  # Remove if present
        super().__init__(*args, **kwargs)
        logger.info("🔄 Legacy RAGEngine wrapper initialized (using bulletproof engine)")
    
    def ask(self, question: str, top_k_retriever: int = 5, top_k_reader: int = 1) -> List[Dict]:
        """
        Legacy compatibility method.
        
        NOTE: This now returns raw context instead of trying to use FARMReader.
        The actual question answering should be done by the GGUF generation engine.
        """
        logger.warning("⚠️ Legacy ask() method called - returning raw context only")
        
        context_chunks = self.retrieve_context(question, top_k=top_k_retriever)
        
        # Return in legacy format for compatibility
        results = []
        for i, chunk in enumerate(context_chunks):
            results.append({
                'answer': chunk,  # Raw context chunk
                'score': 1.0 - (i * 0.1),  # Fake decreasing scores
                'context': chunk,
                'meta': {'source': 'retrieval_only'}
            })
        
        return results
