import re
import time
import json
import requests
import random
from knowledge_app.config import GROQ_API_KEY

def process_content_for_question(content, title):
    topic = title.strip().split('|')[0].strip()
    sentences = re.split(r'[.!?]', content)
    for sent in sentences:
        if topic.lower() in sent.lower() and (' is ' in sent or ' are ' in sent):
            return f"What best describes {topic}?\n{sent.strip()}"
    return f"Which of the following is true about {topic}?"

def generate_options(content, title):
    topic = title.strip().split('|')[0].strip()
    sentences = re.split(r'[.!?]', content)
    correct = next((s.strip() for s in sentences if topic.lower() in s.lower() and (' is ' in s or ' are ' in s)), sentences[0] if sentences else f"A true statement about {topic}.")

    distractors = []
    for _ in range(3):
        if 'not' not in correct:
            distractor = re.sub(r' is ', ' is not ', correct, count=1)
        else:
            distractor = correct.replace(' not', '', 1)
        distractor += " (not always true)"
        distractors.append(distractor)

    options = [correct] + distractors
    random.shuffle(options)
    return options

def get_question_from_groq(topic, difficulty, mode_type=None, submode=None):
    """
    Generate a multiple-choice question using the Groq API.

    Args:
        topic (str): The topic for the question
        difficulty (str): Difficulty level (e.g., "High School", "Bachelor", "Master", "PhD")
        mode_type (str, optional): Mode type for the question
        submode (str, optional): Submode for the question (e.g., "Numerical", "Conceptual")

    Returns:
        str or None: The generated question text if successful, None if there was an error
    """
    # Check if API key is available
    if not GROQ_API_KEY:
        print("ERROR: GROQ_API_KEY not found in environment variables!")
        print("HINT: Make sure you have a .env file with GROQ_API_KEY=your_key and that load_dotenv() is called before accessing it.")
        return None  # Return None to trigger retry in the calling code

    # Log the key length (safely) to help diagnose key issues
    key_length = len(GROQ_API_KEY) if GROQ_API_KEY else 0
    print(f"DEBUG: GROQ_API_KEY length: {key_length} characters")
    if key_length < 20:  # Most API keys are longer than this
        print("WARNING: GROQ_API_KEY seems suspiciously short. Please verify it's correct.")

    # --- Enhanced difficulty and submode instructions ---
    difficulty_instruction = difficulty
    if difficulty == "PhD" or difficulty == "Master":
        difficulty_instruction = f"an advanced {difficulty} level, focusing on complex theoretical or analytical aspects"
    elif difficulty == "Bachelor":
        difficulty_instruction = f"a university {difficulty} level, requiring application of principles"
    elif difficulty == "High School":
        difficulty_instruction = f"a challenging {difficulty} level (e.g., AP Physics, A-Level, or equivalent college-preparatory)"

    submode_instruction = ""
    options_instruction = ""
    if submode and submode.strip():
        submode_instruction = f"The question MUST specifically test '{submode}' understanding - this is a strict requirement. "
        if submode == "Numerical":
            submode_instruction += "IMPORTANT: The question MUST require numerical calculation and not be theory-based or conceptual. It MUST involve applying formulas, unit conversions, or multi-step calculations appropriate for the difficulty level. The question should include numerical values that the student must use to calculate the answer. The options MUST be numerical values with appropriate units. "
            options_instruction = "For numerical questions, ensure the incorrect options are plausible and represent common calculation errors or misconceptions. Include at least one option that would result from a common mistake in the calculation process. The options should be similar in structure and precision to the correct answer. All options MUST be numerical values with appropriate units. "
        elif submode == "Conceptual":
            submode_instruction += "It should test deep understanding of concepts, relationships between concepts, and application of principles, not just definitions or simple recall. "
            options_instruction = "For conceptual questions, ensure the incorrect options are plausible misconceptions or partial understandings that a student might reasonably believe. The options should be similar in length, specificity, and technical language to the correct answer. "
        else:
            options_instruction = "Ensure all options are plausible and of similar length and structure. Avoid obviously incorrect options that can be eliminated without domain knowledge. "

    # Enhanced prompt with clearer formatting instructions and stronger emphasis on topic and submode
    prompt = (
        f"Generate ONE challenging and insightful multiple-choice science question SPECIFICALLY about '{topic}' - the question MUST be directly related to {topic} and not just general science. "
        f"The difficulty level should be {difficulty_instruction}. "
        f"{submode_instruction}"
        f"IMPORTANT: The question MUST be about '{topic}' specifically, not a general science question that could apply to any topic. "
        f"The question should require critical thinking or application of principles, not just simple recall. "
        f"Do NOT generate overly simple arithmetic problems or basic definition questions unless the topic is explicitly about definitions at an elementary level. "
        f"Provide 4 distinct options (A, B, C, D), clearly mark the correct answer letter, "
        f"AND PROVIDE A BRIEF, ACCURATE EXPLANATION for why the correct answer is correct and why the other options are incorrect. "
        f"{options_instruction}"
        f"Ensure the question is non-trivial for the specified level and that the options are genuinely challenging to distinguish for someone at that level. "
        f"Return the question, options, correct answer letter, and explanation in plain text, clearly labeled. "
        f"IMPORTANT: Follow this EXACT format with these EXACT labels:\n"
        f"Question: [The question text]\n"
        f"A) [Option A text]\n"
        f"B) [Option B text]\n"
        f"C) [Option C text]\n"
        f"D) [Option D text]\n"
        f"Correct Answer: [Single letter A, B, C, or D]\n"
        f"Explanation: [Your explanation text]"
    )

    headers = {
        "Authorization": f"Bearer {GROQ_API_KEY}",
        "Content-Type": "application/json"
    }

    # Create a more specific system message that emphasizes following the topic and submode requirements
    system_message = "You are a specialized science quiz generator that creates well-formatted multiple-choice questions. "
    if submode == "Numerical":
        system_message += "You MUST create numerical questions that require calculations and have numerical answers with appropriate units. You should NOT create theory-based or conceptual questions when asked for numerical ones. "

    system_message += f"When given a specific topic like '{topic}', you MUST create questions specifically about that topic, not general science questions. "
    system_message += "Follow the user's instructions precisely regarding topic, difficulty, and question type."

    data = {
        "model": "llama3-70b-8192",
        "messages": [
            {"role": "system", "content": system_message},
            {"role": "user", "content": prompt}
        ],
        "max_tokens": 512,
        "temperature": 0.5,  # Further reduced for more consistent adherence to instructions
        "top_p": 0.9
    }

    print(f"DEBUG: Sending request to Groq API for topic: '{topic}', difficulty: '{difficulty}', submode: '{submode}'")

    try:
        # Add timeout to prevent hanging requests
        response = requests.post(
            "https://api.groq.com/openai/v1/chat/completions", 
            headers=headers, 
            data=json.dumps(data), 
            timeout=30  # Increased timeout for reliability
        )

        # Log response status and headers for debugging
        print(f"DEBUG: Groq API response status code: {response.status_code}")

        # Handle specific HTTP status codes with detailed error messages
        if response.status_code == 401:
            print("ERROR: Groq API key is invalid or unauthorized (401). Please check your key.")
            print("HINT: Verify your key at https://console.groq.com/keys and ensure it's active.")
            return None

        if response.status_code == 403:
            print("ERROR: Forbidden access to Groq API (403). Your account may be suspended or the key doesn't have permission.")
            return None

        if response.status_code == 429:
            print("ERROR: Rate limit exceeded (429). Waiting before retry...")
            print("HINT: Check your Groq account tier limits or reduce request frequency.")
            time.sleep(3)  # Increased wait time
            return None

        if response.status_code >= 500:
            print(f"ERROR: Groq API server error ({response.status_code}). The service might be experiencing issues.")
            return None

        # Check for other HTTP errors
        try:
            response.raise_for_status()
        except requests.exceptions.HTTPError as e:
            print(f"ERROR: HTTP error from Groq API: {e}")
            print(f"Response text: {response.text[:500]}")  # Print first 500 chars of response
            return None

        # Process successful response
        try:
            result = response.json()
        except json.JSONDecodeError as e:
            print(f"ERROR: Failed to parse JSON response from Groq: {e}")
            print(f"Raw response: {response.text[:500]}")
            return None

        # Check if the response has the expected structure
        if "choices" not in result or not result["choices"]:
            print(f"ERROR: No choices in Groq API response: {result}")
            return None

        if "message" not in result["choices"][0] or "content" not in result["choices"][0]["message"]:
            print(f"ERROR: Unexpected response format from Groq API: {result}")
            return None

        content = result["choices"][0]["message"]["content"]
        print(f"DEBUG: Successfully received response from Groq API ({len(content)} characters)")
        return content

    except requests.exceptions.Timeout as e:
        print(f"ERROR: Timeout when connecting to Groq API: {e}")
        print("HINT: Check your internet connection or try again later.")
        return None

    except requests.exceptions.ConnectionError as e:
        print(f"ERROR: Connection error when fetching from Groq: {e}")
        print("HINT: Check your internet connection, firewall settings, or proxy configuration.")
        return None

    except requests.exceptions.RequestException as e:
        print(f"ERROR: Network error when fetching from Groq: {e}")
        return None

    except Exception as e:
        print(f"ERROR: Unexpected error when processing Groq response: {e}")
        import traceback
        traceback.print_exc()
        return None
