# core/quiz_logic.py

difficulty_labels = ["Elementary", "High School", "Bachelor", "Master", "PhD"]

# You can expand this with XP, streaks, history, etc.
difficulty_network = {}  # Format: {username: current_difficulty_index}

difficulty_levels = {}  # Format: {username: "easy" | "medium" | "hard"}
correct_streaks = {}  # Format: {username: streak_count}

def get_user_difficulty(username):
    return difficulty_labels[difficulty_network.get(username, 1)]  # Default: High School

def adjust_difficulty(username, correct, quiz_mode="Serious"):
    """Adjust question difficulty based on user performance and quiz mode"""
    global difficulty_levels
    current_level = difficulty_levels.get(username, "medium")
    
    if quiz_mode == "Serious":
        # More aggressive difficulty adjustment in serious mode
        if correct:
            if current_level == "easy":
                difficulty_levels[username] = "medium"
            elif current_level == "medium":
                difficulty_levels[username] = "hard"
        else:
            if current_level == "hard":
                difficulty_levels[username] = "medium"
            elif current_level == "medium":
                difficulty_levels[username] = "easy"
    else:  # Casual mode
        # Gentler difficulty adjustment
        correct_streak = correct_streaks.get(username, 0)
        if correct:
            correct_streak += 1
            if correct_streak >= 3:  # Require longer streaks to increase difficulty
                if current_level == "easy":
                    difficulty_levels[username] = "medium"
                elif current_level == "medium":
                    difficulty_levels[username] = "hard"
                correct_streak = 0
        else:
            correct_streak = 0
            if current_level == "hard":
                difficulty_levels[username] = "medium"
            elif current_level == "medium":
                difficulty_levels[username] = "easy"
        correct_streaks[username] = correct_streak

def reset_user_difficulty(username):
    difficulty_network[username] = 1

def get_difficulty_index(username):
    return difficulty_network.get(username, 1)

def get_all_user_difficulties():
    return difficulty_network.copy()

def get_question_type(mode, submode):
    """Determine question type based on mode and submode"""
    if mode == "Serious":
        if submode == "Numerical":
            return "calculation"
        elif submode == "Conceptual":
            return "theory"
        else:
            return "mixed"  # Mix of numerical and conceptual
    else:  # Casual mode
        # Casual mode focuses more on engaging, visual questions
        if submode == "Numerical":
            return "visual_calculation"
        elif submode == "Conceptual":
            return "visual_concept"
        else:
            return "visual_mixed"
