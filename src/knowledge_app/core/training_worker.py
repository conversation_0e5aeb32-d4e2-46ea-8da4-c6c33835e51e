"""
Training worker implementation for background model training
"""

import os
import sys
import logging
import torch
import psutil
import time
import gc
from typing import Dict, Any, Optional
from PyQt5.QtCore import QThread, pyqtSignal
from pathlib import Path
from .model_manager import ModelManager
from .training_presets import PresetConfig
try:
    from torch.amp import autocast, GradScaler
except ImportError:
    # Fallback for older PyTorch versions
    try:
        from torch.cuda.amp import autocast, GradScaler
    except ImportError:
        # If neither available, create dummy classes
        class autocast:
            def __init__(self, *args, **kwargs): pass
            def __enter__(self): return self
            def __exit__(self, *args): pass

        class GradScaler:
            def __init__(self, *args, **kwargs): pass
            def scale(self, loss): return loss
            def step(self, optimizer): optimizer.step()
            def update(self): pass
from torch.nn.parallel import DistributedDataParallel

logger = logging.getLogger(__name__)

class TrainingWorker(QThread):
    """Worker thread for AI model training"""
    
    # Progress signals
    progress = pyqtSignal(str)  # Training progress message
    phase_progress = pyqtSignal(int)  # Current phase progress (0-100)
    memory_update = pyqtSignal(dict)  # Memory usage statistics
    finished = pyqtSignal(bool, str)  # Success status and message
    
    def __init__(self, model_manager: ModelManager, config: PresetConfig):
        """
        Initialize the training worker
        
        Args:
            model_manager: Model manager instance
            config: Training configuration
        """
        super().__init__()
        self._model_manager = model_manager
        self._config = config.to_dict()
        self._should_stop = False
        self._current_model = None
        self._optimizer = None
        # Initialize scaler based on device availability
        if torch.cuda.is_available():
            try:
                # Try new API first
                from torch.amp import GradScaler as NewGradScaler
                self._scaler = NewGradScaler('cuda')
            except (ImportError, TypeError):
                # Fallback to old API
                self._scaler = GradScaler()
        else:
            # No scaler needed for CPU training
            self._scaler = None
        self._memory_check_interval = 100  # Check memory every 100 steps (reduced CPU overhead)
        self._last_memory_check = 0
        self._memory_pressure_count = 0
        self._max_memory_pressure = 3  # Max consecutive memory pressure events
        self._batch_accumulation = 1  # Dynamic batch accumulation
        
    def run(self):
        """Run the training process"""
        try:
            logger.info("Starting training worker...")

            # FORCE CUDA INITIALIZATION IN THREAD
            if torch.cuda.is_available():
                # Initialize CUDA context in this thread
                torch.cuda.init()
                torch.cuda.set_device(0)
                # Create a dummy tensor to establish CUDA context
                dummy = torch.tensor([1.0]).cuda()
                logger.info(f"CUDA context established in thread: {dummy.device}")
                del dummy

                # Reinitialize scaler now that CUDA is available in thread
                try:
                    from torch.amp import GradScaler as NewGradScaler
                    self._scaler = NewGradScaler('cuda')
                    logger.info("Scaler reinitialized for CUDA in thread")
                except (ImportError, TypeError):
                    self._scaler = GradScaler()
                    logger.info("Scaler reinitialized with fallback API")
            else:
                logger.warning("CUDA not available in training thread")
                self._scaler = None

            # Initialize model with optimized settings
            self.progress.emit("Initializing model...")
            logger.info("Initializing model...")
            self._current_model = self._model_manager.create_model_architecture()
            logger.info(f"Model initialized: {type(self._current_model)}")

            # Ensure model is on the correct device
            device = self._model_manager.device
            self._current_model = self._current_model.to(device)
            logger.info(f"Model moved to device: {device}")

            if torch.cuda.is_available():
                logger.info("CUDA available, setting up GPU training...")
                # Set model to training mode and enable optimizations
                self._current_model.train()

                # Enable mixed precision training
                logger.info("GPU training enabled with autocast")

                # Enable DDP if multiple GPUs available
                if torch.cuda.device_count() > 1:
                    self._current_model = DistributedDataParallel(self._current_model)
                    logger.info("Distributed training enabled")
            else:
                logger.info("Using CPU training")
                self._current_model.train()

            # Prepare training data with GPU optimization
            self.progress.emit("Preparing training data...")
            logger.info("Preparing training data...")
            train_data = self._model_manager.prepare_training_data()
            logger.info(f"Training data prepared: {len(train_data)} examples")

            # Create batches from the training data
            batch_size = self._config.get('batch_size', 4)  # Small batch size for memory efficiency
            batched_data = []
            for i in range(0, len(train_data), batch_size):
                batch = train_data[i:i + batch_size]
                if len(batch) > 0:
                    batched_data.append(batch)

            logger.info(f"Created {len(batched_data)} batches with batch size {batch_size}")
            train_data = batched_data

            # Configure optimizer with memory optimization
            logger.info("Configuring optimizer...")
            self._optimizer = self._model_manager.configure_optimizer(
                self._current_model.parameters(),
                learning_rate=self._config['initial_lr'],
                weight_decay=self._config['weight_decay'],
                optimizer_type=self._config.get('optimizer_type', 'adam')
            )
            logger.info(f"Optimizer configured: {type(self._optimizer)}")

            # Train model with GPU optimizations
            self.progress.emit("Starting training...")
            logger.info("Starting training loop...")
            success = self._train_model_optimized(
                model=self._current_model,
                optimizer=self._optimizer,
                train_data=train_data,
                epochs=self._config['epochs'],
                batch_size=self._config['batch_size']
            )

            if success:
                logger.info("Training completed successfully")
                self.finished.emit(True, "Training completed successfully")
            else:
                logger.warning("Training failed to converge")
                self.finished.emit(False, "Training failed to converge")

        except Exception as e:
            logger.error(f"Training error: {e}", exc_info=True)
            self.finished.emit(False, str(e))
        finally:
            logger.info("Cleaning up training worker...")
            self._cleanup()
            
    def _train_model_optimized(self, model, optimizer, train_data, epochs, batch_size):
        """Train model with GPU optimizations"""
        try:
            logger.info(f"Starting optimized training: {epochs} epochs, {len(train_data)} batches")

            # DEBUG: Check CUDA availability in this thread
            logger.info(f"DEBUG: torch.cuda.is_available() = {torch.cuda.is_available()}")
            logger.info(f"DEBUG: torch.version.cuda = {torch.version.cuda}")
            logger.info(f"DEBUG: torch.__version__ = {torch.__version__}")
            logger.info(f"DEBUG: torch.__file__ = {torch.__file__}")
            logger.info(f"DEBUG: sys.executable = {sys.executable}")
            logger.info(f"DEBUG: sys.path[0:3] = {sys.path[0:3]}")

            # Try to create a test tensor on GPU
            try:
                test_tensor = torch.tensor([1.0]).cuda()
                logger.info(f"DEBUG: GPU tensor test successful: {test_tensor.device}")
            except Exception as e:
                logger.error(f"DEBUG: GPU tensor test failed: {e}")

            # Check if we can import a fresh torch
            try:
                import importlib
                importlib.reload(torch)
                logger.info(f"DEBUG: After reload - torch.__version__ = {torch.__version__}")
                logger.info(f"DEBUG: After reload - torch.cuda.is_available() = {torch.cuda.is_available()}")
            except Exception as e:
                logger.error(f"DEBUG: Torch reload failed: {e}")

            # FORCE GPU USAGE - Ensure model is on GPU if CUDA is available
            if torch.cuda.is_available():
                target_device = torch.device('cuda:0')
                model = model.to(target_device)
                logger.info(f"FORCED model to GPU: {target_device}")
            else:
                target_device = torch.device('cpu')
                logger.error(f"❌ CUDA not available in training thread, using CPU: {target_device}")

            device = next(model.parameters()).device
            logger.info(f"Training device: {device}")

            # Verify device is correct
            if torch.cuda.is_available() and device.type != 'cuda':
                logger.error(f"ERROR: Model should be on CUDA but is on {device}")
                # Force move again
                model = model.to('cuda:0')
                device = next(model.parameters()).device
                logger.info(f"FORCED AGAIN - Training device: {device}")

            # Set model to training mode
            model.train()
            logger.info("Model set to training mode")

            for epoch in range(epochs):
                if self._should_stop:
                    logger.info("Training stopped by user request")
                    break

                logger.info(f"Starting epoch {epoch + 1}/{epochs}")
                self.progress.emit(f"Training epoch {epoch + 1}/{epochs}")
                epoch_loss = 0.0
                num_batches = len(train_data)

                for batch_idx, batch in enumerate(train_data):
                    if self._should_stop:
                        break

                    # Check memory and adjust batch accumulation if needed
                    if batch_idx % self._memory_check_interval == 0:
                        self._check_and_adjust_memory()

                    # Process each example in the batch
                    batch_loss = 0.0
                    optimizer.zero_grad(set_to_none=True)

                    for example in batch:
                        if self._should_stop:
                            break

                        # Extract input and target from example
                        if isinstance(example, (tuple, list)) and len(example) == 2:
                            inputs, targets = example

                            # Ensure proper tensor types
                            if inputs.dtype != torch.long:
                                inputs = inputs.long()
                            if targets.dtype != torch.long:
                                targets = targets.long()

                            # Move to device
                            inputs = inputs.to(device, non_blocking=True)
                            targets = targets.to(device, non_blocking=True)

                            # Handle language modeling (inputs and targets are token sequences)
                            if inputs.dim() == 1:
                                inputs = inputs.unsqueeze(0)  # Add batch dimension
                            if targets.dim() == 1:
                                targets = targets.unsqueeze(0)  # Add batch dimension

                            # Training step with device-appropriate autocast
                            if device.type == 'cuda' and self._scaler is not None:
                                # GPU training with mixed precision using new API
                                with autocast('cuda', enabled=True):
                                    outputs = model(inputs)

                                    # For language modeling, we predict next tokens
                                    if hasattr(outputs, 'logits'):
                                        logits = outputs.logits
                                    else:
                                        logits = outputs

                                    # Shift targets for next-token prediction
                                    shift_logits = logits[..., :-1, :].contiguous()
                                    shift_labels = targets[..., 1:].contiguous()

                                    # Calculate loss
                                    loss = torch.nn.functional.cross_entropy(
                                        shift_logits.view(-1, shift_logits.size(-1)),
                                        shift_labels.view(-1),
                                        ignore_index=-100
                                    )

                                    # Scale loss for gradient accumulation
                                    loss = loss / len(batch) / self._batch_accumulation

                                # Backward pass with gradient scaling
                                self._scaler.scale(loss).backward()
                            else:
                                # CPU training without mixed precision
                                outputs = model(inputs)

                                # For language modeling, we predict next tokens
                                if hasattr(outputs, 'logits'):
                                    logits = outputs.logits
                                else:
                                    logits = outputs

                                # Shift targets for next-token prediction
                                shift_logits = logits[..., :-1, :].contiguous()
                                shift_labels = targets[..., 1:].contiguous()

                                # Calculate loss
                                loss = torch.nn.functional.cross_entropy(
                                    shift_logits.view(-1, shift_logits.size(-1)),
                                    shift_labels.view(-1),
                                    ignore_index=-100
                                )

                                # Scale loss for gradient accumulation
                                loss = loss / len(batch) / self._batch_accumulation

                                # Backward pass
                                loss.backward()

                            # Accumulate loss for this example
                            batch_loss += loss.item()

                    # Update epoch loss with batch average
                    epoch_loss += batch_loss / len(batch)

                    # Optimizer step after accumulation
                    if (batch_idx + 1) % self._batch_accumulation == 0:
                        if device.type == 'cuda' and self._scaler is not None:
                            self._scaler.step(optimizer)
                            self._scaler.update()
                        else:
                            optimizer.step()

                    # Update progress
                    batch_progress = (batch_idx + 1) / num_batches * 100
                    overall_progress = ((epoch * num_batches + batch_idx + 1) / (epochs * num_batches)) * 100
                    self.phase_progress.emit(int(overall_progress))

                    # Log progress every batch for better UI updates
                    if (batch_idx + 1) % 1 == 0:
                        avg_loss = epoch_loss / (batch_idx + 1)
                        self.progress.emit(f"Epoch {epoch + 1}/{epochs}, Batch {batch_idx + 1}/{num_batches}, Loss: {avg_loss:.4f}")

                # Log epoch completion
                avg_epoch_loss = epoch_loss / num_batches
                self.progress.emit(f"Epoch {epoch + 1} completed. Average loss: {avg_epoch_loss:.4f}")
                    
            return True
            
        except Exception as e:
            logger.error(f"Error in optimized training: {e}")
            return False
            
    def _check_and_adjust_memory(self):
        """Check memory usage and adjust training parameters"""
        try:
            current_time = time.time()
            if current_time - self._last_memory_check < 1:  # Limit check frequency
                return
                
            self._last_memory_check = current_time
            
            if torch.cuda.is_available():
                # Get memory stats
                allocated = torch.cuda.memory_allocated() / 1024**3  # GB
                reserved = torch.cuda.memory_reserved() / 1024**3  # GB
                total = torch.cuda.get_device_properties(0).total_memory / 1024**3  # GB
                
                # Emit memory stats
                self.memory_update.emit({
                    'allocated': allocated,
                    'reserved': reserved,
                    'total': total
                })
                
                # Check for memory pressure
                if allocated / total > 0.9:  # Over 90% usage
                    self._memory_pressure_count += 1
                    
                    if self._memory_pressure_count >= self._max_memory_pressure:
                        # Increase batch accumulation
                        self._batch_accumulation *= 2
                        logger.warning(f"Increased batch accumulation to {self._batch_accumulation}")
                        
                        # Clear cache
                        torch.cuda.empty_cache()
                        
                        self._memory_pressure_count = 0
                else:
                    self._memory_pressure_count = 0
                    
            # Check system memory
            memory = psutil.virtual_memory()
            if memory.percent > 90:  # Over 90% usage
                gc.collect()  # Force garbage collection
                
        except Exception as e:
            logger.error(f"Error checking memory: {e}")
            
    def _cleanup(self):
        """Clean up resources with GPU optimization"""
        try:
            # Move model to CPU first
            if self._current_model is not None:
                self._current_model.cpu()
                del self._current_model
                self._current_model = None
            
            # Clear optimizer
            if self._optimizer is not None:
                del self._optimizer
                self._optimizer = None
            
            # Clear scaler
            if hasattr(self, '_scaler') and self._scaler is not None:
                del self._scaler
                self._scaler = None
            
            # Aggressive GPU cleanup
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
                torch.cuda.reset_peak_memory_stats()
                
            # Force garbage collection
            gc.collect()
            
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")
            
    def stop(self):
        """Stop the training process gracefully"""
        self._should_stop = True 