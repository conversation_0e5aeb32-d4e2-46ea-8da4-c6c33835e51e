"""
Enterprise Application Bootstrapper

This module replaces the God Object pattern with a clean, professional
bootstrapping system that uses dependency injection and separation of concerns.

Responsibilities:
- Initialize logging system
- Configure dependency injection container
- Set up core services
- Create and configure QApplication
- Initialize main window
- Handle application lifecycle
"""

import sys
import logging
from typing import Optional
from pathlib import Path

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

from .startup_optimizer import get_startup_optimizer, start_phase, end_phase
from .enterprise_di_container import get_container, configure_services
from ..utils.pyqt_compat import setup_pyqt_compatibility
from ..utils.error_handler import <PERSON>rror<PERSON><PERSON><PERSON>, ErrorSeverity

logger = logging.getLogger(__name__)

class ApplicationBootstrapper:
    """
    Professional application bootstrapper that replaces the ApplicationManager God Object.
    
    This class follows the Single Responsibility Principle and uses dependency injection
    to manage the application lifecycle cleanly.
    """
    
    def __init__(self):
        self.container = None
        self.app = None
        self.main_window = None
        self.error_handler = None
        self.is_initialized = False
        
    def bootstrap(self) -> int:
        """
        Bootstrap the entire application using enterprise patterns.

        Returns:
            int: Application exit code
        """
        try:
            # Initialize startup optimizer
            optimizer = get_startup_optimizer()

            # Phase 1: Initialize core systems
            start_phase("core_systems")
            self._initialize_logging()
            self._setup_dependency_injection()
            self._setup_error_handling()
            end_phase("core_systems")

            # Phase 2: Setup PyQt compatibility
            start_phase("pyqt_compatibility")
            self._setup_pyqt_compatibility()
            end_phase("pyqt_compatibility")

            # Phase 3: Create QApplication
            start_phase("qt_application")
            self._create_qt_application()
            end_phase("qt_application")

            # Phase 4: Initialize core services (deferred for performance)
            start_phase("core_services")
            self._initialize_core_services_lazy()
            end_phase("core_services")

            # Phase 5: Create main window
            start_phase("main_window")
            self._create_main_window()
            end_phase("main_window")

            # Phase 6: Setup application lifecycle
            start_phase("lifecycle")
            self._setup_application_lifecycle()
            end_phase("lifecycle")

            # Phase 7: Show main window and run
            start_phase("application_run")
            result = self._run_application()
            end_phase("application_run")

            # Log startup summary
            optimizer.log_startup_summary()

            return result

        except Exception as e:
            self._handle_bootstrap_error(e)
            return 1
    
    def _initialize_logging(self):
        """Initialize the logging system"""
        try:
            # Import here to avoid circular dependencies
            from ..utils.logging_config import LogManager
            from pathlib import Path

            logging_manager = LogManager()
            log_dir = Path(__file__).parent.parent.parent.parent / 'logs'
            logging_manager.initialize(log_dir, env="production")

            logger.info("🚀 Knowledge App - Enterprise Edition Starting...")
            logger.info("✅ Logging system initialized")

        except Exception as e:
            # Fallback logging setup
            logging.basicConfig(
                level=logging.INFO,
                format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            logger.error(f"Failed to initialize advanced logging: {e}")
    
    def _setup_dependency_injection(self):
        """Setup the enterprise dependency injection container"""
        try:
            self.container = configure_services()
            logger.info("✅ Enterprise DI container configured")
            
        except Exception as e:
            logger.error(f"Failed to setup DI container: {e}")
            raise
    
    def _setup_error_handling(self):
        """Setup centralized error handling"""
        try:
            self.error_handler = self.container.resolve(ErrorHandler)
            logger.info("✅ Error handling system initialized")
            
        except Exception as e:
            logger.warning(f"Could not setup advanced error handling: {e}")
            # Create basic error handler
            self.error_handler = ErrorHandler()
    
    def _setup_pyqt_compatibility(self):
        """Setup PyQt compatibility and environment"""
        try:
            compatibility_info = setup_pyqt_compatibility()
            logger.info(f"✅ PyQt compatibility setup: {compatibility_info['pyqt_version']}")
            
        except Exception as e:
            logger.warning(f"PyQt compatibility setup failed: {e}")
    
    def _create_qt_application(self):
        """Create and configure the QApplication instance"""
        try:
            self.app = QApplication(sys.argv)
            self.app.setStyle('Fusion')
            self.app.setApplicationName("Knowledge App")
            self.app.setApplicationVersion("2.0.0")
            self.app.setOrganizationName("Knowledge Systems")
            
            # Register with DI container
            self.container.register_instance(QApplication, self.app)
            
            logger.info("✅ QApplication created and configured")
            
        except Exception as e:
            logger.error(f"Failed to create QApplication: {e}")
            raise
    
    def _initialize_core_services_lazy(self):
        """Initialize core services with lazy loading for better startup performance"""
        try:
            # Initialize only critical services immediately
            critical_services = [
                'ResourceManager',
                'ErrorHandler'
            ]

            for service_name in critical_services:
                try:
                    service_type = self._get_service_type(service_name)
                    if service_type and self.container.is_registered(service_type):
                        service = self.container.resolve(service_type)
                        logger.debug(f"✅ {service_name} initialized (critical)")
                except Exception as e:
                    logger.warning(f"Could not initialize critical service {service_name}: {e}")

            # Defer heavy services to background loading
            heavy_services = [
                'GPUManager',
                'StorageManager',
                'MemoryManager',
                'ModelManager',
                'MCQManager'
            ]

            def init_heavy_services():
                """Initialize heavy services in background"""
                for service_name in heavy_services:
                    try:
                        service_type = self._get_service_type(service_name)
                        if service_type and self.container.is_registered(service_type):
                            service = self.container.resolve(service_type)
                            logger.debug(f"✅ {service_name} initialized (background)")
                    except Exception as e:
                        logger.warning(f"Could not initialize background service {service_name}: {e}")

                logger.info("✅ Background services initialization completed")

            # Start background initialization
            from .startup_optimizer import defer_heavy_initialization
            defer_heavy_initialization("heavy_services", init_heavy_services)

            logger.info("✅ Core services initialization started (lazy)")

        except Exception as e:
            logger.error(f"Failed to initialize core services: {e}")
            raise

    def _initialize_core_services(self):
        """Legacy method - kept for compatibility"""
        return self._initialize_core_services_lazy()
    
    def _get_service_type(self, service_name: str):
        """Get service type by name (helper method)"""
        try:
            if service_name == 'ResourceManager':
                from ..utils.resource_manager import ResourceManager
                return ResourceManager
            elif service_name == 'GPUManager':
                from .gpu_manager import GPUManager
                return GPUManager
            elif service_name == 'StorageManager':
                from .storage_manager import StorageManager
                return StorageManager
            elif service_name == 'MemoryManager':
                from .memory_manager import MemoryManager
                return MemoryManager
            elif service_name == 'ModelManager':
                from .model_manager import ModelManager
                return ModelManager
            elif service_name == 'MCQManager':
                from .mcq_manager import MCQManager
                return MCQManager
        except ImportError:
            return None
    
    def _create_main_window(self):
        """Create the enterprise main application window"""
        try:
            # Import the enterprise main window
            from ..ui.enterprise_main_window import create_enterprise_main_window

            # Create enterprise main window with MVC architecture
            self.main_window = create_enterprise_main_window()

            # Ensure MCQ manager is properly initialized for enterprise main window
            self._setup_mcq_manager_for_enterprise_window()

            # Register with DI container
            self.container.register_instance(type(self.main_window), self.main_window)

            logger.info("✅ Enterprise Main Window created with MVC architecture")

        except ImportError:
            # Fallback to legacy main window if enterprise version not available
            logger.warning("Enterprise main window not available, falling back to legacy")
            try:
                from ..ui.main_window import MainWindow
                self.main_window = MainWindow()

                # Ensure MCQ manager is properly initialized for legacy main window
                self._setup_mcq_manager_for_legacy_window()

                self.container.register_instance(MainWindow, self.main_window)
                logger.info("✅ Legacy main window created")
            except Exception as fallback_error:
                logger.error(f"Failed to create fallback main window: {fallback_error}")
                raise

        except Exception as e:
            logger.error(f"Failed to create enterprise main window: {e}")
            # Try simple main window as fallback
            try:
                logger.info("Attempting simple main window fallback...")
                from ..ui.simple_main_window import create_simple_main_window
                self.main_window = create_simple_main_window()
                self.container.register_instance(type(self.main_window), self.main_window)
                logger.info("✅ Simple main window created as fallback")
            except Exception as simple_error:
                logger.error(f"Simple main window fallback failed: {simple_error}")
                raise

    def _setup_mcq_manager_for_legacy_window(self):
        """Setup MCQ manager for legacy main window"""
        try:
            from .mcq_manager import MCQManager

            # Get or create MCQ manager from DI container
            if self.container.is_registered(MCQManager):
                mcq_manager = self.container.resolve(MCQManager)
            else:
                # Create MCQ manager if not registered
                from .config_manager import get_config
                config = get_config()
                mcq_manager = MCQManager(config)
                self.container.register_instance(MCQManager, mcq_manager)

            # Ensure the main window has the _get_mcq_manager method
            if hasattr(self.main_window, 'mcq_manager'):
                self.main_window.mcq_manager = mcq_manager
                logger.info("✅ MCQ manager attached to legacy main window")

        except Exception as e:
            logger.warning(f"Could not setup MCQ manager for legacy window: {e}")

    def _setup_mcq_manager_for_enterprise_window(self):
        """Setup MCQ manager for enterprise main window"""
        try:
            from .mcq_manager import MCQManager

            # Get or create MCQ manager from DI container
            if self.container.is_registered(MCQManager):
                mcq_manager = self.container.resolve(MCQManager)
            else:
                # Create MCQ manager if not registered
                from .config_manager import get_config
                config = get_config()
                mcq_manager = MCQManager(config)
                self.container.register_instance(MCQManager, mcq_manager)

            # Ensure the enterprise main window has the MCQ manager
            if hasattr(self.main_window, 'mcq_manager'):
                self.main_window.mcq_manager = mcq_manager
                logger.info("✅ MCQ manager attached to enterprise main window")

        except Exception as e:
            logger.warning(f"Could not setup MCQ manager for enterprise window: {e}")
    
    def _setup_application_lifecycle(self):
        """Setup application lifecycle management"""
        try:
            # Setup periodic state saving
            state_timer = QTimer()
            state_timer.timeout.connect(self._save_application_state)
            state_timer.start(30000)  # Save every 30 seconds
            
            # Setup shutdown handling
            try:
                from ..utils.shutdown_manager import ShutdownManager
                shutdown_manager = self.container.resolve(ShutdownManager)
                if shutdown_manager:
                    self.app.aboutToQuit.connect(shutdown_manager.shutdown)
                    logger.debug("✅ Shutdown manager connected to QApplication")
            except Exception as e:
                logger.warning(f"Could not setup shutdown manager: {e}")
            
            logger.info("✅ Application lifecycle configured")
            
        except Exception as e:
            logger.warning(f"Could not setup full lifecycle management: {e}")
    
    def _save_application_state(self):
        """Save application state periodically"""
        try:
            # This would save application state for crash recovery
            logger.debug("Application state saved")
        except Exception as e:
            logger.warning(f"Failed to save application state: {e}")
    
    def _run_application(self) -> int:
        """Run the application event loop"""
        try:
            if self.main_window:
                self.main_window.show()
                logger.info("🎉 Knowledge App started successfully!")
                return self.app.exec_()
            else:
                logger.error("No main window to show")
                return 1
                
        except Exception as e:
            logger.error(f"Application runtime error: {e}")
            return 1
    
    def _handle_bootstrap_error(self, error: Exception):
        """Handle errors during bootstrap process"""
        error_msg = f"Application bootstrap failed: {error}"
        logger.error(error_msg)
        
        if self.error_handler:
            self.error_handler.handle_error(
                error,
                context="Application Bootstrap",
                severity=ErrorSeverity.CRITICAL,
                show_dialog=True
            )
        else:
            # Fallback error display
            print(f"CRITICAL ERROR: {error_msg}")

def bootstrap_application() -> int:
    """
    Main entry point for bootstrapping the application.
    
    Returns:
        int: Application exit code
    """
    bootstrapper = ApplicationBootstrapper()
    return bootstrapper.bootstrap()
