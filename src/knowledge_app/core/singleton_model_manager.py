"""
Singleton Model Manager - Single Source of Truth for Model Operations

This module implements a thread-safe singleton pattern for managing AI models,
eliminating race conditions and memory corruption issues.

Key Features:
- Thread-safe singleton pattern
- State machine with explicit states
- Request queue for serialized operations
- Proper memory management
- Comprehensive logging with thread IDs
"""

# CRITICAL MEMORY FIX: Import only lightweight modules during startup
import threading
import queue
import time
import logging
import weakref
import gc
from enum import Enum
from typing import Optional, Dict, Any, Callable
from dataclasses import dataclass

# CRITICAL MEMORY FIX: Heavy ML imports will be done lazily when model operations are first used

logger = logging.getLogger(__name__)

class ModelState(Enum):
    """Model states for the state machine"""
    UNLOADED = "unloaded"
    LOADING = "loading"
    READY = "ready"
    UNLOADING = "unloading"
    ERROR = "error"

@dataclass
class ModelRequest:
    """Request for model operations"""
    action: str  # 'load', 'unload', 'generate'
    model_path: Optional[str] = None
    prompt: Optional[str] = None
    callback: Optional[Callable] = None
    request_id: str = ""

class SingletonModelManager:
    """
    Thread-safe singleton model manager that ensures only one instance exists
    and serializes all model operations to prevent race conditions.
    """
    
    _instance = None
    _lock = threading.Lock()
    _initialized = False
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if self._initialized:
            return
            
        with self._lock:
            if self._initialized:
                return
                
            self._initialized = True
            
            # State management
            self._state = ModelState.UNLOADED
            self._state_lock = threading.RLock()
            
            # Model storage
            self._model = None
            self._tokenizer = None
            self._current_model_path = None
            
            # Request queue and worker thread
            self._request_queue = queue.Queue()
            self._worker_thread = None
            self._stop_worker = threading.Event()
            
            # Memory management
            self._memory_threshold = 0.85
            self._last_cleanup = time.time()
            
            # Logging with thread IDs
            self._setup_logging()
            
            # Start worker thread
            self._start_worker()
            
            logger.info(f"🏗️ SingletonModelManager initialized - Thread: {threading.current_thread().name}")
    
    def _setup_logging(self):
        """Setup enhanced logging with thread IDs"""
        formatter = logging.Formatter(
            '%(asctime)s - %(threadName)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        # Ensure we have thread names for debugging
        if not threading.current_thread().name.startswith('MainThread'):
            threading.current_thread().name = f"ModelManager-{threading.get_ident()}"
    
    def _start_worker(self):
        """Start the worker thread for processing requests"""
        if self._worker_thread is None or not self._worker_thread.is_alive():
            self._worker_thread = threading.Thread(
                target=self._worker_loop,
                name="ModelManager-Worker",
                daemon=True
            )
            self._worker_thread.start()
            logger.info(f"🚀 Model worker thread started - Thread: {self._worker_thread.name}")
    
    def _worker_loop(self):
        """Main worker loop that processes requests sequentially"""
        logger.info(f"🔄 Worker loop started - Thread: {threading.current_thread().name}")
        
        while not self._stop_worker.is_set():
            try:
                # Get request with timeout
                request = self._request_queue.get(timeout=1.0)
                
                logger.info(f"📥 Processing request: {request.action} - Thread: {threading.current_thread().name}")
                
                # Process request based on action
                if request.action == 'load':
                    self._handle_load_request(request)
                elif request.action == 'unload':
                    self._handle_unload_request(request)
                elif request.action == 'generate':
                    self._handle_generate_request(request)
                else:
                    logger.error(f"❌ Unknown request action: {request.action}")
                
                # Mark request as done
                self._request_queue.task_done()
                
            except queue.Empty:
                continue
            except Exception as e:
                logger.error(f"❌ Error in worker loop: {e}", exc_info=True)
                self._set_state(ModelState.ERROR)
        
        logger.info(f"🛑 Worker loop stopped - Thread: {threading.current_thread().name}")
    
    def _set_state(self, new_state: ModelState):
        """Thread-safe state change"""
        with self._state_lock:
            old_state = self._state
            self._state = new_state
            logger.info(f"🔄 State change: {old_state.value} → {new_state.value} - Thread: {threading.current_thread().name}")
    
    def get_state(self) -> ModelState:
        """Get current state thread-safely"""
        with self._state_lock:
            return self._state
    
    def _handle_load_request(self, request: ModelRequest):
        """Handle model loading request"""
        try:
            # Check current state
            if self.get_state() in [ModelState.LOADING, ModelState.READY]:
                logger.warning(f"⚠️ Model already loading/loaded, ignoring request")
                return
            
            self._set_state(ModelState.LOADING)
            
            # Perform memory cleanup before loading
            self._cleanup_memory()
            
            # Load model (simplified for example)
            logger.info(f"📥 Loading model: {request.model_path}")
            
            # Simulate model loading (replace with actual implementation)
            time.sleep(0.1)  # Simulate loading time
            
            # Store model info
            self._current_model_path = request.model_path
            
            self._set_state(ModelState.READY)
            logger.info(f"✅ Model loaded successfully: {request.model_path}")
            
            # Call callback if provided
            if request.callback:
                request.callback(True, "Model loaded successfully")
                
        except Exception as e:
            logger.error(f"❌ Error loading model: {e}", exc_info=True)
            self._set_state(ModelState.ERROR)
            if request.callback:
                request.callback(False, f"Error loading model: {e}")
    
    def _handle_unload_request(self, request: ModelRequest):
        """Handle model unloading request"""
        try:
            if self.get_state() == ModelState.UNLOADED:
                logger.warning(f"⚠️ Model already unloaded")
                return
            
            self._set_state(ModelState.UNLOADING)
            
            # Unload model
            logger.info(f"📤 Unloading model: {self._current_model_path}")
            
            # Clear model references
            self._model = None
            self._tokenizer = None
            self._current_model_path = None
            
            # Cleanup memory
            self._cleanup_memory()
            
            self._set_state(ModelState.UNLOADED)
            logger.info(f"✅ Model unloaded successfully")
            
            if request.callback:
                request.callback(True, "Model unloaded successfully")
                
        except Exception as e:
            logger.error(f"❌ Error unloading model: {e}", exc_info=True)
            self._set_state(ModelState.ERROR)
            if request.callback:
                request.callback(False, f"Error unloading model: {e}")
    
    def _handle_generate_request(self, request: ModelRequest):
        """Handle text generation request"""
        try:
            if self.get_state() != ModelState.READY:
                error_msg = f"Model not ready for generation. Current state: {self.get_state().value}"
                logger.error(f"❌ {error_msg}")
                if request.callback:
                    request.callback(False, error_msg)
                return
            
            logger.info(f"🎯 Generating text for prompt: {request.prompt[:50]}...")
            
            # Simulate text generation (replace with actual implementation)
            result = f"Generated response for: {request.prompt}"
            
            logger.info(f"✅ Text generation completed")
            
            if request.callback:
                request.callback(True, result)
                
        except Exception as e:
            logger.error(f"❌ Error generating text: {e}", exc_info=True)
            if request.callback:
                request.callback(False, f"Error generating text: {e}")
    
    def _cleanup_memory(self):
        """Perform memory cleanup"""
        try:
            # Force garbage collection
            collected = gc.collect()
            logger.debug(f"🧹 Garbage collection freed {collected} objects")
            
            # Clear GPU cache if available
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
                allocated = torch.cuda.memory_allocated() / 1024**2  # MB
                logger.debug(f"🎮 GPU memory allocated: {allocated:.1f}MB")
            
            self._last_cleanup = time.time()
            
        except Exception as e:
            logger.error(f"❌ Error during memory cleanup: {e}")
    
    # Public API methods
    
    def load_model_async(self, model_path: str, callback: Optional[Callable] = None) -> str:
        """Load model asynchronously"""
        request_id = f"load_{int(time.time() * 1000)}"
        request = ModelRequest(
            action='load',
            model_path=model_path,
            callback=callback,
            request_id=request_id
        )
        
        self._request_queue.put(request)
        logger.info(f"📤 Load request queued: {model_path} - ID: {request_id}")
        return request_id
    
    def unload_model_async(self, callback: Optional[Callable] = None) -> str:
        """Unload model asynchronously"""
        request_id = f"unload_{int(time.time() * 1000)}"
        request = ModelRequest(
            action='unload',
            callback=callback,
            request_id=request_id
        )
        
        self._request_queue.put(request)
        logger.info(f"📤 Unload request queued - ID: {request_id}")
        return request_id
    
    def generate_async(self, prompt: str, callback: Optional[Callable] = None) -> str:
        """Generate text asynchronously"""
        request_id = f"generate_{int(time.time() * 1000)}"
        request = ModelRequest(
            action='generate',
            prompt=prompt,
            callback=callback,
            request_id=request_id
        )
        
        self._request_queue.put(request)
        logger.info(f"📤 Generate request queued - ID: {request_id}")
        return request_id
    
    def shutdown(self):
        """Shutdown the model manager"""
        logger.info(f"🛑 Shutting down SingletonModelManager")
        
        # Stop worker thread
        self._stop_worker.set()
        if self._worker_thread and self._worker_thread.is_alive():
            self._worker_thread.join(timeout=5.0)
        
        # Cleanup model
        if self.get_state() != ModelState.UNLOADED:
            self._handle_unload_request(ModelRequest(action='unload'))
        
        logger.info(f"✅ SingletonModelManager shutdown complete")

# Global instance getter
def get_model_manager() -> SingletonModelManager:
    """Get the singleton model manager instance"""
    return SingletonModelManager()
