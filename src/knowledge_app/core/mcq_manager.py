"""
MCQ Manager - Unified interface for online and offline MCQ generation
Handles switching between Groq API and local models seamlessly
"""

import logging
from typing import Dict, List, Any, Optional
import asyncio

# CRITICAL MEMORY FIX: Defer torch import to reduce startup memory
# torch will be imported lazily when needed for cleanup
torch = None

logger = logging.getLogger(__name__)

class MCQManager:
    """Unified manager for MCQ generation with online/offline mode switching"""
    
    def __init__(self, config=None):
        self.config = config
        self.instant_generator = None
        self.offline_generator = None
        self.online_generator = None
        self.rag_generator = None
        self._offline_mode = False
        self._rag_mode = True  # Enable RAG by default when available
        self._instant_mode = True  # Enable instant generation by default

        # Initialize post-processor for enhanced quality
        self.post_processor = None
        self._use_post_processing = True

        # Initialize generators
        self._initialize_generators()
    
    def _initialize_generators(self):
        """Initialize instant, RAG, offline, and online generators"""
        try:
            # Initialize instant generator (highest priority - always works!)
            from .instant_mcq_generator import InstantMCQGenerator
            self.instant_generator = InstantMCQGenerator()
            logger.info("⚡ Instant MCQ generator ready - no model loading required!")

            # Initialize RAG-enhanced generator (high quality when available)
            from .rag_mcq_generator import RAGMCQGenerator
            self.rag_generator = RAGMCQGenerator()
            rag_ready = self.rag_generator.initialize()
            if rag_ready:
                logger.info("🔍 RAG MCQ generator ready with knowledge base")
            else:
                logger.warning("⚠️ RAG MCQ generator not ready - will use fallback")

            # Initialize offline generator (lazy loading - only when needed)
            from .offline_mcq_generator import OfflineMCQGenerator
            self.offline_generator = OfflineMCQGenerator()

            # Don't initialize offline generator during startup - only when explicitly requested
            logger.info("🏠 Offline MCQ generator ready for lazy initialization...")

            # Initialize online generator (existing inference system)
            from .inference import CloudInference
            if self.config:
                self.online_generator = CloudInference(self.config)

            # Initialize post-processor for enhanced quality
            if self._use_post_processing:
                try:
                    from .mcq_post_processor import MCQPostProcessor
                    self.post_processor = MCQPostProcessor()
                    logger.info("🔧 MCQ post-processor initialized for enhanced quality")
                except Exception as e:
                    logger.warning(f"⚠️ Failed to initialize post-processor: {e}")
                    self._use_post_processing = False

            logger.info("✅ MCQ Manager initialized with instant, RAG, offline, and online capabilities")

        except Exception as e:
            logger.error(f"❌ Error initializing MCQ Manager: {e}")
    
    def set_offline_mode(self, offline: bool):
        """Set the MCQ generation mode"""
        self._offline_mode = offline
        logger.info(f"MCQ generation mode set to: {'Offline' if offline else 'Online'}")

    def set_rag_mode(self, rag_enabled: bool):
        """Enable or disable RAG-enhanced MCQ generation"""
        self._rag_mode = rag_enabled
        logger.info(f"RAG mode set to: {'Enabled' if rag_enabled else 'Disabled'}")

    def set_instant_mode(self, instant_enabled: bool):
        """Enable or disable instant MCQ generation"""
        self._instant_mode = instant_enabled
        logger.info(f"Instant mode set to: {'Enabled' if instant_enabled else 'Disabled'}")

    def is_offline_mode(self) -> bool:
        """Check if currently in offline mode"""
        return self._offline_mode

    def is_rag_mode(self) -> bool:
        """Check if RAG mode is enabled"""
        return self._rag_mode

    def is_instant_mode(self) -> bool:
        """Check if instant mode is enabled"""
        return self._instant_mode

    def is_rag_available(self) -> bool:
        """Check if RAG mode is available"""
        return self.rag_generator is not None and self.rag_generator.is_initialized

    def is_instant_available(self) -> bool:
        """Check if instant mode is available (always True!)"""
        if self.instant_generator is None:
            # Try to reinitialize instant generator if it's missing
            try:
                from .instant_mcq_generator import InstantMCQGenerator
                self.instant_generator = InstantMCQGenerator()
                logger.info("⚡ Instant MCQ generator reinitialized")
            except Exception as e:
                logger.error(f"❌ Failed to reinitialize instant generator: {e}")
                return False
        return self.instant_generator is not None
    
    def is_offline_available(self) -> bool:
        """Check if offline mode is available (without initializing)"""
        if not self.offline_generator:
            return False

        try:
            # Check if already initialized
            if self.offline_generator.is_initialized:
                return True

            # BULLETPROOF FIX: Only check for stable GGUF engine availability
            # NO MORE unstable LocalModelInference - it's been permanently removed
            try:
                from .gguf_model_inference import GGUFModelInference
                # Check if we can create the engine (this is lightweight)
                return True  # GGUF engine available and stable
            except ImportError as e:
                logger.warning(f"⚠️ GGUF model inference not available: {e}")
                return False
        except Exception as e:
            logger.error(f"❌ Error checking offline availability: {e}")
            return False
    
    def is_online_available(self) -> bool:
        """Check if online mode is available"""
        if not self.online_generator:
            return False
        
        try:
            # Check if we have API configuration
            return hasattr(self.online_generator, 'client') and self.online_generator.client is not None
        except Exception:
            return False
    
    async def generate_quiz_async(self, context: str, difficulty: str = "medium",
                                 use_advanced: bool = True, cognitive_level: str = "understanding") -> Dict[str, Any]:
        """
        Generate a single MCQ question using the best available method.

        Args:
            context: The text content to generate questions from
            difficulty: Question difficulty level
            use_advanced: Whether to use advanced generation techniques
            cognitive_level: Bloom's taxonomy level (remembering, understanding, applying, analyzing, evaluating, creating)

        Returns:
            Dictionary containing question, options, correct answer, and explanation
        """
        try:
            logger.info(f"🎯 Generating MCQ: context='{context[:50]}...', difficulty='{difficulty}', advanced={use_advanced}")

            # Try advanced RAG generation first if enabled
            if use_advanced and self._rag_mode and self.is_rag_available():
                logger.info("🧠 Using advanced RAG MCQ generation with validation")
                try:
                    result = await self._generate_advanced_rag(context, difficulty, cognitive_level)
                    if result and result.get('question'):
                        # Apply post-processing for enhanced quality
                        return self._apply_post_processing(result, {
                            'topic': context,
                            'difficulty': difficulty,
                            'cognitive_level': cognitive_level
                        })
                    else:
                        logger.warning("⚠️ Advanced RAG generation returned empty result")
                except Exception as e:
                    logger.warning(f"⚠️ Advanced RAG generation failed: {e}")

            # Try offline generation first if enabled and available (7B models)
            if self._offline_mode and self.is_offline_available():
                logger.info("🧠 Using offline 7B model MCQ generation")
                try:
                    result = await self._generate_offline(context, difficulty)
                    if result and result.get('question'):
                        return result
                    else:
                        logger.warning("⚠️ Offline generation returned empty result")
                except Exception as e:
                    logger.warning(f"⚠️ Offline generation failed: {e}")

            # Try RAG-enhanced generation if enabled and available
            if self._rag_mode and self.is_rag_available():
                logger.info("🔍 Using RAG-enhanced MCQ generation")
                try:
                    result = await self._generate_rag(context, difficulty)
                    if result and result.get('question'):
                        return result
                    else:
                        logger.warning("⚠️ RAG generation returned empty result")
                except Exception as e:
                    logger.warning(f"⚠️ RAG generation failed: {e}")

            # Try online generation if available
            if self.is_online_available():
                logger.info("🌐 Using online MCQ generation")
                try:
                    result = await self._generate_online(context, difficulty)
                    if result and result.get('question'):
                        return result
                    else:
                        logger.warning("⚠️ Online generation returned empty result")
                except Exception as e:
                    logger.warning(f"⚠️ Online generation failed: {e}")

            # Use instant generation as fallback only
            if self.is_instant_available():
                logger.info("⚡ Using instant MCQ generation (fallback - no model loading)")
                try:
                    result = await self._generate_instant(context, difficulty)
                    if result and result.get('question'):
                        return result
                    else:
                        logger.warning("⚠️ Instant generation returned empty result")
                except Exception as e:
                    logger.warning(f"⚠️ Instant generation failed: {e}")

            # If all else fails, use emergency fallback
            logger.warning("⚠️ All generation methods failed, using emergency fallback")
            return await self._generate_fallback(context, difficulty)

        except Exception as e:
            logger.error(f"❌ MCQ generation failed: {e}")
            return await self._generate_fallback(context, difficulty)
    
    async def _generate_offline(self, context: str, difficulty: str) -> Dict[str, Any]:
        """Generate MCQ using offline local models"""
        if not self.offline_generator:
            raise RuntimeError("Offline generator not available")

        # Smart initialization check - only initialize if truly needed
        if not self.offline_generator.is_initialized:
            logger.info("🔄 Initializing offline generator for MCQ generation...")
            if not self.offline_generator.initialize():
                raise RuntimeError("Failed to initialize offline generator")
        else:
            logger.debug("✅ Offline generator already initialized - reusing existing model")

        logger.info("🏠 Generating MCQ using local models (offline)")
        return await self.offline_generator.generate_quiz_async(context, difficulty)

    async def _generate_instant(self, context: str, difficulty: str) -> Dict[str, Any]:
        """Generate MCQ using instant content analysis (no model loading)"""
        if not self.instant_generator:
            raise RuntimeError("Instant generator not available")

        logger.info("⚡ Generating instant MCQ using content analysis")
        return await self.instant_generator.generate_quiz_async(context, difficulty)

    async def _generate_rag(self, context: str, difficulty: str) -> Dict[str, Any]:
        """Generate MCQ using RAG-enhanced approach"""
        if not self.rag_generator:
            raise RuntimeError("RAG generator not available")

        logger.info("🔍 Generating RAG-enhanced MCQ using knowledge base")
        return await self.rag_generator.generate_quiz_async(context, difficulty, use_rag=True)

    async def _generate_advanced_rag(self, context: str, difficulty: str, cognitive_level: str = "understanding") -> Dict[str, Any]:
        """Generate MCQ using advanced RAG with validation and quality control"""
        try:
            # Import the advanced generator
            from .advanced_rag_mcq_generator import AdvancedRAGMCQGenerator, CognitiveLevel

            # Map string to enum
            cognitive_map = {
                "remembering": CognitiveLevel.REMEMBERING,
                "understanding": CognitiveLevel.UNDERSTANDING,
                "applying": CognitiveLevel.APPLYING,
                "analyzing": CognitiveLevel.ANALYZING,
                "evaluating": CognitiveLevel.EVALUATING,
                "creating": CognitiveLevel.CREATING
            }

            cognitive_enum = cognitive_map.get(cognitive_level.lower(), CognitiveLevel.UNDERSTANDING)

            # Create advanced generator
            advanced_generator = AdvancedRAGMCQGenerator(
                rag_engine=getattr(self.rag_generator, 'rag_engine', None),
                model_interface=getattr(self.offline_generator, 'local_inference', None) if self.offline_generator else None
            )

            # Generate with advanced techniques
            result = await advanced_generator.generate_grounded_mcq(
                topic=context,
                difficulty=difficulty,
                cognitive_level=cognitive_enum
            )

            logger.info("✅ Advanced RAG MCQ generation completed")
            return result

        except Exception as e:
            logger.error(f"❌ Advanced RAG generation failed: {e}")
            # Fall back to regular RAG generation
            return await self._generate_rag(context, difficulty)

    async def _generate_online(self, context: str, difficulty: str) -> Dict[str, Any]:
        """Generate MCQ using online API (Groq)"""
        if not self.online_generator:
            raise RuntimeError("Online generator not available")
        
        logger.info("🌐 Generating MCQ using external API (online)")
        return await self.online_generator.generate_quiz_async(context, difficulty)
    
    async def _generate_fallback(self, context: str, difficulty: str) -> Dict[str, Any]:
        """Generate MCQ using fallback method when primary method fails"""
        try:
            # Try RAG mode if not already tried and available
            if not self._rag_mode and self.is_rag_available():
                logger.warning("⚠️ Primary generation failed, trying RAG fallback")
                return await self._generate_rag(context, difficulty)

            # Try the opposite mode as fallback
            if self._offline_mode and self.is_online_available():
                logger.warning("⚠️ Offline generation failed, trying online fallback")
                return await self._generate_online(context, difficulty)
            elif not self._offline_mode and self.is_offline_available():
                logger.warning("⚠️ Online generation failed, trying offline fallback")
                return await self._generate_offline(context, difficulty)
            else:
                # Generate a basic fallback question
                logger.warning("⚠️ All modes failed, using basic fallback")
                return self._generate_basic_fallback(context)

        except Exception as e:
            logger.error(f"❌ Fallback generation also failed: {e}")
            return self._generate_basic_fallback(context)
    
    def _generate_basic_fallback(self, context: str) -> Dict[str, Any]:
        """Generate a very basic fallback question using simple text analysis"""
        import re

        # Extract key words from context
        words = re.findall(r'\b[A-Za-z]{4,}\b', context.lower())
        common_words = {'that', 'this', 'with', 'from', 'they', 'have', 'been', 'were', 'will', 'would', 'could', 'should'}
        key_words = [w for w in words if w not in common_words][:10]

        # Generate context-aware question
        if key_words:
            primary_topic = key_words[0].title()
            question = f"Based on the content about {primary_topic}, what is the main focus of the discussion?"

            # Generate options based on content
            options = {
                "A": f"Technical aspects of {primary_topic}",
                "B": f"General overview of {primary_topic}",
                "C": f"Historical development of {primary_topic}",
                "D": f"Practical applications of {primary_topic}"
            }

            explanation = f"This question focuses on the main theme related to {primary_topic} discussed in the content."
        else:
            # Ultra-basic fallback
            question = "Based on the provided content, what is the main topic being discussed?"
            options = {
                "A": "Technical specifications and details",
                "B": "General concepts and principles",
                "C": "Historical background and context",
                "D": "Practical applications and examples"
            }
            explanation = "This is a general question generated when AI model generation is unavailable."

        return {
            "question": question,
            "options": options,
            "correct": "B",  # Usually the general overview is a safe choice
            "explanation": explanation
        }
    
    def generate_multiple_questions(self, context: str, num_questions: int = 5, 
                                  difficulty: str = "medium") -> List[Dict[str, Any]]:
        """
        Generate multiple MCQ questions using the current mode
        
        Args:
            context: The text content to generate questions from
            num_questions: Number of questions to generate
            difficulty: Question difficulty level
            
        Returns:
            List of question dictionaries
        """
        try:
            # Try instant generation first (no model loading!)
            if self._instant_mode and self.is_instant_available():
                logger.info(f"⚡ Generating {num_questions} instant MCQs (no model loading)")
                return self.instant_generator.generate_multiple_questions(context, num_questions, difficulty)

            # Try RAG-enhanced generation if enabled and available
            if self._rag_mode and self.is_rag_available():
                logger.info(f"🔍 Generating {num_questions} RAG-enhanced MCQs")
                return self.rag_generator.generate_multiple_questions(context, num_questions, difficulty)

            if self._offline_mode and self.offline_generator:
                logger.info(f"🏠 Generating {num_questions} MCQs using local models")
                return self.offline_generator.generate_multiple_questions(context, num_questions, difficulty)
            else:
                # For online mode, generate questions one by one
                logger.info(f"🌐 Generating {num_questions} MCQs using external API")
                return asyncio.run(self._generate_multiple_online(context, num_questions, difficulty))

        except Exception as e:
            logger.error(f"❌ Multiple question generation failed: {e}")
            return []

    def cleanup(self):
        """Clean up MCQ manager resources to prevent memory leaks and crashes"""
        try:
            logger.info("🧹 Starting MCQ manager cleanup...")

            # Clean up offline generator
            if self.offline_generator:
                try:
                    if hasattr(self.offline_generator, 'local_inference') and self.offline_generator.local_inference:
                        if hasattr(self.offline_generator.local_inference, 'unload_model'):
                            logger.info("🧹 Unloading offline model...")
                            self.offline_generator.local_inference.unload_model()
                    self.offline_generator = None
                except Exception as e:
                    logger.warning(f"⚠️ Error cleaning up offline generator: {e}")

            # Clean up online generator
            if self.online_generator:
                try:
                    # Close any open connections
                    if hasattr(self.online_generator, 'cleanup'):
                        self.online_generator.cleanup()
                    self.online_generator = None
                except Exception as e:
                    logger.warning(f"⚠️ Error cleaning up online generator: {e}")

            # Clean up RAG generator
            if self.rag_generator:
                try:
                    if hasattr(self.rag_generator, 'cleanup'):
                        self.rag_generator.cleanup()
                    self.rag_generator = None
                except Exception as e:
                    logger.warning(f"⚠️ Error cleaning up RAG generator: {e}")

            # Clean up instant generator
            if self.instant_generator:
                try:
                    if hasattr(self.instant_generator, 'cleanup'):
                        self.instant_generator.cleanup()
                    self.instant_generator = None
                except Exception as e:
                    logger.warning(f"⚠️ Error cleaning up instant generator: {e}")

            # Force garbage collection
            import gc
            gc.collect()
            gc.collect()  # Call twice for better cleanup

            # Clear CUDA cache if available (only if torch was imported)
            try:
                # CRITICAL MEMORY FIX: Import torch lazily only for cleanup
                import torch
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()
                    torch.cuda.synchronize()  # Wait for all CUDA operations to complete
            except ImportError:
                # torch not available, skip CUDA cleanup
                pass

            logger.info("✅ MCQ manager cleanup completed successfully")

        except Exception as e:
            logger.error(f"❌ Error during MCQ manager cleanup: {e}")
            # Still try basic cleanup
            try:
                import gc
                gc.collect()
                try:
                    # CRITICAL MEMORY FIX: Import torch lazily only for cleanup
                    import torch
                    if torch.cuda.is_available():
                        torch.cuda.empty_cache()
                except ImportError:
                    pass
            except:
                pass
    
    async def _generate_multiple_online(self, context: str, num_questions: int, difficulty: str) -> List[Dict[str, Any]]:
        """Generate multiple questions using online API"""
        questions = []
        
        for i in range(num_questions):
            try:
                question = await self._generate_online(context, difficulty)
                if question:
                    questions.append(question)
                    logger.info(f"Generated online question {i+1}/{num_questions}")
            except Exception as e:
                logger.warning(f"Failed to generate online question {i+1}: {e}")
        
        return questions

    async def generate_quiz_async(self, quiz_params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Orchestrates the entire high-quality MCQ generation process.
        1. Retrieves REAL content using RAG (not instructions!)
        2. Selects the best generator
        3. Generates the question using pure content
        4. Returns validated result

        This fixes the "prompt leakage" problem by ensuring content separation.
        """
        try:
            topic = quiz_params.get('topic', 'General Knowledge')
            difficulty = quiz_params.get('difficulty', 'medium')
            cognitive_level = quiz_params.get('cognitive_level', 'understanding')

            logger.info(f"🎯 Starting intelligent MCQ generation for '{topic}' (difficulty: {difficulty})")

            # --- STEP 1: The Grounded Scholar - Retrieve REAL Content ---
            logger.info(f"🔍 Using RAG Engine to retrieve factual content for topic: '{topic}'")

            real_content = await self._retrieve_pure_content(topic)
            if not real_content:
                logger.warning(f"⚠️ No content found for '{topic}'. Activating Layer 2: Intelligent Fallback.")
                real_content = self._get_curated_fallback_content(topic)

                # Final validation - if even curated content fails, use emergency fallback
                if not real_content or len(real_content.strip()) < 200:
                    logger.error(f"❌ No curated fallback content found for '{topic}'. Using emergency fallback.")
                    return self._generate_basic_fallback(topic)

            logger.info(f"📚 Using content of length {len(real_content)} characters for generation.")

            # --- STEP 2: Select Best Generator ---
            generator = self._select_best_generator()
            if not generator:
                logger.error("❌ No suitable generator available")
                return self._generate_basic_fallback(topic)

            # --- STEP 3: Generate Question with Pure Content ---
            logger.info("🧠 Generating question using pure content (no instruction leakage)")

            if hasattr(generator, 'generate_quiz_async'):
                # Use async method if available
                result = await generator.generate_quiz_async(real_content, topic, difficulty, cognitive_level)
            else:
                # Fallback to sync method
                result = generator.generate_mcq(real_content, topic, difficulty, cognitive_level)

            # --- STEP 4: Apply Post-Processing ---
            if result and self._use_post_processing:
                result = self._apply_post_processing(result, quiz_params)

            logger.info("✅ High-quality MCQ generation completed successfully")
            return result

        except Exception as e:
            logger.error(f"❌ MCQ generation orchestration failed: {e}")
            return self._generate_basic_fallback(quiz_params.get('topic', 'Error'))

    async def _retrieve_pure_content(self, topic: str) -> str:
        """
        Retrieve pure factual content (not instructions) for the topic.

        LAYER 1: FORTIFY THE RAG PIPELINE - Validates content quality
        """
        try:
            logger.info(f"🔍 Using RAG Engine to retrieve context for topic: '{topic}'")

            # Try advanced RAG first
            if self._rag_mode and self.is_rag_available():
                try:
                    from .advanced_rag_mcq_generator import AdvancedRAGMCQGenerator
                    if hasattr(self, 'advanced_rag_generator') and self.advanced_rag_generator:
                        # Use the RAG engine to get pure content
                        rag_engine = self.advanced_rag_generator.rag_engine
                        if rag_engine and hasattr(rag_engine, 'retrieve_context'):
                            context_chunks = await rag_engine.retrieve_context(topic, top_k=3)
                            if context_chunks:
                                pure_content = "\n\n".join(context_chunks)

                                # **CRITICAL VALIDATION STEP - LAYER 1**
                                if len(pure_content.strip()) >= 200:  # Minimum character threshold
                                    logger.info(f"✅ RAG retrieved substantial content ({len(pure_content)} chars)")
                                    return pure_content
                                else:
                                    logger.warning(f"⚠️ RAG content for '{topic}' is insufficient ({len(pure_content)} chars < 200). Activating intelligent fallback.")
                except Exception as e:
                    logger.warning(f"⚠️ Advanced RAG content retrieval failed: {e}")

            # Try basic RAG fallback
            if hasattr(self, 'rag_generator') and self.rag_generator:
                try:
                    # Get context from basic RAG generator
                    context = self.rag_generator._get_context_for_topic(topic)
                    if context and len(context.strip()) >= 200:  # Apply same validation
                        logger.info(f"✅ Basic RAG retrieved substantial content ({len(context)} chars)")
                        return context
                    elif context:
                        logger.warning(f"⚠️ Basic RAG content for '{topic}' is insufficient ({len(context)} chars < 200)")
                except Exception as e:
                    logger.warning(f"⚠️ Basic RAG content retrieval failed: {e}")

            # If we reach here, RAG failed to provide substantial content
            logger.warning(f"⚠️ RAG context for '{topic}' is insufficient. Activating intelligent fallback.")
            return None

        except Exception as e:
            logger.error(f"❌ Content retrieval failed: {e}")
            return None

    def _get_curated_fallback_content(self, topic: str) -> str:
        """
        LAYER 2: INTELLIGENT FALLBACK WITH CURATED CONTENT

        Provides high-quality, pre-written context for common topics when the RAG
        engine fails to find user-specific content. This is our intelligent safety net.
        """
        logger.info(f"🛡️ Activating Layer 2: Intelligent Fallback for topic '{topic}'")

        # High-quality curated contexts with substantial detail
        curated_content = {
            "magnetism": """
Magnetism is a physical phenomenon produced by the motion of electric charge, resulting in attractive and repulsive forces between objects. The magnetic field, an invisible field that exerts magnetic force, is created by moving electric charges. The Lorentz force equation, F = q(E + v × B), describes the force on a point charge due to electromagnetic fields. A key aspect of magnetism is the existence of magnetic dipoles, or north and south poles, where field lines emerge from the north and enter the south.

Ferromagnetic materials like iron, nickel, and cobalt can be permanently magnetized due to their unpaired electrons that create magnetic domains. When these domains align, the material becomes magnetized. The Curie temperature is the critical temperature above which ferromagnetic materials lose their permanent magnetic properties and become paramagnetic.

Electromagnetic induction, discovered by Michael Faraday, demonstrates that a changing magnetic field induces an electric field. This principle underlies the operation of electric generators, transformers, and motors. Faraday's law quantifies this relationship: the induced EMF equals the negative rate of change of magnetic flux. Lenz's law states that induced currents flow in a direction to oppose the change that created them, ensuring energy conservation.
            """,
            "physics": """
Classical mechanics, a major branch of physics, is governed by Newton's three laws of motion. The first law, the law of inertia, states an object remains at rest or in uniform motion unless acted upon by a net external force. The second law quantifies force as mass times acceleration (F=ma). The third law states that for every action, there is an equal and opposite reaction. These principles are fundamental to understanding the motion of macroscopic objects.

Thermodynamics studies heat, temperature, and energy transfer in systems. The first law of thermodynamics states that energy cannot be created or destroyed, only transformed from one form to another. The second law introduces the concept of entropy, stating that the entropy of an isolated system always increases over time. This explains why heat flows spontaneously from hot to cold objects and why perpetual motion machines are impossible.

Quantum mechanics revolutionized our understanding of matter and energy at atomic scales. Wave-particle duality reveals that particles exhibit both wave and particle characteristics depending on the experimental setup. The uncertainty principle, formulated by Heisenberg, states that the position and momentum of a particle cannot be simultaneously measured with perfect precision. This fundamental limitation arises from the wave nature of matter.
            """,
            "chemistry": """
Chemical bonding involves the forces that hold atoms together in molecules and compounds. The primary types are ionic bonds, formed by the electrostatic attraction between oppositely charged ions, and covalent bonds, where atoms share one or more pairs of electrons. The electronegativity difference between atoms determines the type of bond formed. Covalent bonds can be polar or nonpolar depending on the equality of electron sharing.

The periodic table organizes elements by atomic number and reveals periodic trends in properties. Atomic radius generally decreases across a period due to increasing nuclear charge, while ionization energy increases. Down a group, atomic radius increases as electron shells are added. These trends explain chemical reactivity patterns and bonding behavior.

Chemical reactions involve the breaking and forming of bonds, following conservation laws. The law of conservation of mass states that matter cannot be created or destroyed in chemical reactions. Reaction rates depend on factors like concentration, temperature, and catalysts. Activation energy represents the minimum energy required for a reaction to occur, and catalysts lower this barrier without being consumed.
            """,
            "programming": """
Programming involves creating step-by-step instructions for computers to execute tasks. Modern programming languages like Python, Java, and C++ provide abstractions that hide complex machine-level operations. Object-oriented programming organizes code into classes and objects, promoting code reuse, modularity, and maintainability. Encapsulation hides internal implementation details, inheritance allows code reuse through class hierarchies, and polymorphism enables objects to be treated uniformly.

Algorithms are systematic procedures for solving computational problems. Time complexity analysis, expressed in Big O notation, describes how execution time scales with input size. Common complexities include O(1) for constant time, O(log n) for logarithmic, O(n) for linear, and O(n²) for quadratic. Space complexity measures memory usage patterns. Efficient algorithms are crucial for handling large datasets and real-time applications.

Data structures organize and store information efficiently. Arrays provide constant-time random access but fixed size. Linked lists allow dynamic sizing but require sequential access. Hash tables offer average constant-time lookup through key-value mapping. Trees enable hierarchical organization with efficient searching, sorting, and range queries. The choice of data structure significantly impacts algorithm performance.
            """
        }

        # Find the best match for the topic
        topic_lower = topic.lower()
        for key, content in curated_content.items():
            if key in topic_lower or topic_lower in key:
                logger.info(f"✅ Found curated content for '{key}' matching topic '{topic}'")
                return content.strip()

        # If no specific match, provide a substantial generic educational content
        logger.info(f"📚 Using enhanced generic content for topic '{topic}'")
        return f"""
{topic} encompasses a broad range of interconnected concepts, principles, and methodologies that form the foundation of this field of study. Understanding {topic} requires systematic examination of its core components, the relationships between different elements, and their practical applications in real-world scenarios.

The theoretical framework of {topic} is built upon fundamental principles that have been developed and refined through extensive research and practical application. These principles serve as the building blocks for more advanced concepts and provide the logical foundation for problem-solving approaches within this domain.

Practical applications of {topic} demonstrate its relevance and importance in various contexts. Real-world implementations showcase how theoretical knowledge translates into tangible solutions and innovations. The interdisciplinary nature of {topic} means it often intersects with other fields, creating opportunities for cross-pollination of ideas and methodologies.

Critical analysis and systematic thinking are essential skills when working with {topic}. Students and practitioners must develop the ability to evaluate information, identify patterns, and apply appropriate methodologies to solve complex problems. This requires not just memorization of facts, but deep understanding of underlying principles and their interconnections.

Contemporary developments in {topic} continue to evolve, driven by technological advances, new research findings, and changing societal needs. Staying current with these developments is crucial for anyone seeking to master this field and contribute meaningfully to its advancement.
        """.strip()

    def _select_best_generator(self):
        """Select the best available generator for high-quality MCQ generation"""
        try:
            # Prefer advanced RAG generator for content-based generation
            if self._rag_mode and hasattr(self, 'advanced_rag_generator') and self.advanced_rag_generator:
                logger.info("🎯 Selected Advanced RAG Generator")
                return self.advanced_rag_generator

            # Fallback to offline generator
            if self._offline_mode and self.offline_generator:
                logger.info("🎯 Selected Offline Generator")
                return self.offline_generator

            # Last resort: basic RAG generator
            if hasattr(self, 'rag_generator') and self.rag_generator:
                logger.info("🎯 Selected Basic RAG Generator")
                return self.rag_generator

            logger.warning("⚠️ No suitable generator found")
            return None

        except Exception as e:
            logger.error(f"❌ Generator selection failed: {e}")
            return None

    def _generate_basic_fallback(self, topic: str) -> Dict[str, Any]:
        """
        EMERGENCY FALLBACK - Apply Three-Layered Defense even here!

        This method should NEVER generate meta-level questions.
        Instead, it uses our Layer 2 curated content to create meaningful questions.
        """
        logger.warning(f"🚨 EMERGENCY FALLBACK ACTIVATED for topic '{topic}' - Applying Three-Layered Defense")

        # Apply Layer 2: Get curated content even in emergency fallback
        curated_content = self._get_curated_fallback_content(topic)

        if curated_content and len(curated_content.strip()) >= 200:
            # Extract a specific fact from curated content for a real question
            content_words = curated_content.split()

            # Look for specific concepts, equations, or facts
            if "equation" in curated_content.lower() or "formula" in curated_content.lower():
                return {
                    "question": f"According to the fundamental principles of {topic}, which equation or relationship is most significant?",
                    "options": {
                        "A": "The primary mathematical relationship described in the content",
                        "B": "A secondary supporting formula",
                        "C": "An unrelated mathematical expression",
                        "D": "A simplified approximation"
                    },
                    "correct": "A",
                    "explanation": f"Based on the educational content about {topic}, the primary mathematical relationship is the most fundamental concept."
                }
            elif "law" in curated_content.lower() or "principle" in curated_content.lower():
                return {
                    "question": f"Which fundamental law or principle governs the behavior described in {topic}?",
                    "options": {
                        "A": "The primary governing principle mentioned in the content",
                        "B": "A secondary supporting rule",
                        "C": "An exception to the general rule",
                        "D": "A simplified approximation"
                    },
                    "correct": "A",
                    "explanation": f"The fundamental principle governing {topic} is the most important concept to understand."
                }
            else:
                # Extract first meaningful sentence for a content-based question
                sentences = curated_content.split('.')
                if len(sentences) > 1:
                    first_fact = sentences[0].strip()
                    return {
                        "question": f"Based on the educational content, what is true about the fundamental nature of {topic}?",
                        "options": {
                            "A": "It involves the specific mechanisms described in educational materials",
                            "B": "It operates through completely different principles",
                            "C": "It has no practical applications",
                            "D": "It contradicts established scientific understanding"
                        },
                        "correct": "A",
                        "explanation": f"The educational content about {topic} describes specific mechanisms and principles that are fundamental to understanding this topic."
                    }

        # Final emergency fallback - but still avoid meta-level questions
        logger.error(f"🚨 CRITICAL: Even curated content failed for '{topic}' - using absolute emergency fallback")
        return {
            "question": f"In the study of {topic}, which approach is most effective for understanding the underlying mechanisms?",
            "options": {
                "A": "Examining the fundamental processes and their interactions",
                "B": "Memorizing isolated facts without context",
                "C": "Ignoring the theoretical foundation",
                "D": "Focusing only on historical developments"
            },
            "correct": "A",
            "explanation": f"Understanding {topic} requires examining the fundamental processes and how they interact, rather than memorizing isolated information."
        }

    def _apply_post_processing(self, mcq_data: Dict[str, Any], context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Apply post-processing to enhance MCQ quality"""
        try:
            if not self._use_post_processing or not self.post_processor:
                return mcq_data

            # Convert MCQ data to JSON string for processing
            import json
            mcq_json = json.dumps(mcq_data)

            # Process through pipeline
            result = self.post_processor.process_mcq(mcq_json, context)

            if result.success:
                logger.info(f"✅ Post-processing enhanced MCQ quality (score: {result.quality_score:.2f})")
                return result.mcq_data
            else:
                logger.warning(f"⚠️ Post-processing failed: {result.issues}")
                return mcq_data

        except Exception as e:
            logger.error(f"❌ Post-processing error: {e}")
            return mcq_data

    def set_post_processing(self, enabled: bool):
        """Enable or disable post-processing"""
        self._use_post_processing = enabled
        logger.info(f"Post-processing set to: {'Enabled' if enabled else 'Disabled'}")

    def is_post_processing_enabled(self) -> bool:
        """Check if post-processing is enabled"""
        return self._use_post_processing and self.post_processor is not None
    
    def get_mode_status(self) -> Dict[str, Any]:
        """Get current mode status and availability"""
        return {
            "current_mode": "offline" if self._offline_mode else "online",
            "instant_enabled": self._instant_mode,
            "instant_available": self.is_instant_available(),
            "rag_enabled": self._rag_mode,
            "rag_available": self.is_rag_available(),
            "offline_available": self.is_offline_available(),
            "online_available": self.is_online_available(),
            "can_switch": True
        }
    
    def get_mode_info(self) -> str:
        """Get human-readable mode information"""
        instant_status = "⚡ Instant" if self.is_instant_available() and self._instant_mode else ""
        rag_status = "🔍 RAG Enhanced" if self.is_rag_available() and self._rag_mode else ""

        # Instant mode takes priority
        if self._instant_mode and self.is_instant_available():
            return f"⚡ Instant Mode - No model loading required {rag_status}".strip()

        if self._offline_mode:
            if self.is_offline_available():
                return f"🏠 Offline Mode - Using local models {rag_status}".strip()
            else:
                return "⚠️ Offline Mode - Local models not available"
        else:
            if self.is_online_available():
                return f"🌐 Online Mode - Using external API {rag_status}".strip()
            else:
                return "⚠️ Online Mode - API not configured"
    
    def switch_mode(self):
        """Switch between online and offline modes"""
        new_mode = not self._offline_mode
        
        # Check if the new mode is available
        if new_mode and not self.is_offline_available():
            logger.warning("Cannot switch to offline mode - local models not available")
            return False
        elif not new_mode and not self.is_online_available():
            logger.warning("Cannot switch to online mode - API not configured")
            return False
        
        self.set_offline_mode(new_mode)
        return True
    
    def update_config(self, config):
        """Update configuration and reinitialize if needed"""
        self.config = config
        
        # Check if offline mode setting changed
        if config and hasattr(config, 'get_value'):
            offline_mode = config.get_value('mcq_settings.offline_mode', False)
            if offline_mode != self._offline_mode:
                self.set_offline_mode(offline_mode)
        
        # Reinitialize online generator with new config
        try:
            from .inference import CloudInference
            self.online_generator = CloudInference(config)
        except Exception as e:
            logger.warning(f"Failed to update online generator config: {e}")

# Global instance for easy access
_mcq_manager = None

def get_mcq_manager(config=None) -> MCQManager:
    """Get the global MCQ manager instance"""
    global _mcq_manager
    if _mcq_manager is None:
        _mcq_manager = MCQManager(config)
    elif config:
        _mcq_manager.update_config(config)
    return _mcq_manager
