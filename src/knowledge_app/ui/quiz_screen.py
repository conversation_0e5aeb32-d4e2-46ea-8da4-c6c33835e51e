from PyQt5.QtGui import <PERSON>Font, QPalette, QPixmap, QImage
from PyQt5.QtCore import Qt, QPropertyAnimation, QEasingCurve, QTimer, QSize, QObject
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QSizePolicy,
    QRadioButton, QButtonGroup, QTextEdit, QScrollArea, QMessageBox, QFrame,
    QProgressBar, QSpacerItem
)
from datetime import datetime
import logging
from typing import List, Optional, Dict, Any
from PyQt5.QtCore import pyqtSignal

# Ensure GLOBAL_FONT_FAMILY is imported from knowledge_app.fonts
try:
    from knowledge_app.fonts import GLOBAL_FONT_FAMILY
except ImportError:
    GLOBAL_FONT_FAMILY = "Arial" # Fallback font

# Enterprise styling imports
from .enterprise_style_manager import get_style_manager
from .enterprise_design_system import get_design_system
from .style_migration_helper import get_migration_helper

# Enhanced MCQ widget import
from .enhanced_mcq_widget import EnhancedMCQWidget

logger = logging.getLogger(__name__)


class QuizController(QObject):
    """
    Controller for quiz logic following MVC pattern.
    Handles business logic like checking answers, managing timers, and quiz progression.
    """

    # Signals for communicating with the view
    answer_checked = pyqtSignal(bool, str)  # is_correct, correct_answer
    timer_updated = pyqtSignal(int)  # remaining_seconds
    quiz_completed = pyqtSignal(dict)  # final_results
    question_loaded = pyqtSignal(dict)  # question_data

    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_question_data = None
        self.quiz_timer = QTimer()
        self.quiz_timer.timeout.connect(self._on_timer_tick)
        self.time_remaining = 0
        self.quiz_history = []

    def load_question(self, question_data: Dict[str, Any]):
        """Load a new question"""
        self.current_question_data = question_data

        # Set up timer if question has time limit
        time_limit = question_data.get('time_limit', 0)
        if time_limit > 0:
            self.time_remaining = time_limit
            self.quiz_timer.start(1000)  # Update every second

        self.question_loaded.emit(question_data)

    def check_answer(self, selected_option: str) -> bool:
        """Check if the selected answer is correct"""
        if not self.current_question_data:
            return False

        correct_answer = self.current_question_data.get('correct_option_letter', '').upper()
        is_correct = selected_option.upper() == correct_answer

        # Stop timer
        self.quiz_timer.stop()

        # Add to history
        self._add_to_history(selected_option, is_correct)

        # Emit result
        self.answer_checked.emit(is_correct, correct_answer)

        return is_correct

    def _on_timer_tick(self):
        """Handle timer tick"""
        self.time_remaining -= 1
        self.timer_updated.emit(self.time_remaining)

        if self.time_remaining <= 0:
            self.quiz_timer.stop()
            # Auto-submit with no answer
            self._add_to_history('', False, timed_out=True)
            self.answer_checked.emit(False, self.current_question_data.get('correct_option_letter', ''))

    def _add_to_history(self, user_answer: str, is_correct: bool, timed_out: bool = False):
        """Add answer to quiz history"""
        if self.current_question_data:
            history_entry = {
                'question_data': self.current_question_data,
                'user_answer_letter': user_answer,
                'is_correct': is_correct,
                'timed_out': timed_out,
                'timestamp': datetime.now().isoformat()
            }
            self.quiz_history.append(history_entry)

    def get_history(self) -> List[Dict[str, Any]]:
        """Get quiz history"""
        return self.quiz_history.copy()

    def get_history_entry(self, index: int) -> Optional[Dict[str, Any]]:
        """Get specific history entry"""
        if 0 <= index < len(self.quiz_history):
            return self.quiz_history[index]
        return None

class QuizScreen(QWidget):
    """Quiz interface widget - MVC View Component"""

    # Signals
    question_answered = pyqtSignal(dict)  # Emits answer data
    quiz_completed = pyqtSignal()  # Emits when quiz is finished
    answer_submitted = pyqtSignal(str)  # Emits selected option to controller

    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent = parent

        # Initialize enterprise styling system
        self.style_manager = get_style_manager()
        self.design_system = get_design_system()
        self.migration_helper = get_migration_helper()

        # Initialize MVC controller
        self.controller = QuizController(self)
        self._connect_controller_signals()

        self.current_question_data = None  # Initialize question data
        self.question_service = None
        self.app_config = None
        if hasattr(parent, 'colors'):
            self.colors = parent.colors

        # Initialize UI components
        self.option_buttons = []
        self.option_group = None
        self.submit_btn = None
        self.feedback_label = None
        self.explanation_header_label = None
        self.explanation_area = None
        self.progress_bar = None

        # Enhanced MCQ widget
        self.enhanced_mcq_widget = None
        self.use_enhanced_widget = True  # Flag to enable enhanced widget

        self.setup_ui()

    def _connect_controller_signals(self):
        """Connect controller signals to view methods"""
        self.controller.answer_checked.connect(self._on_answer_checked)
        self.controller.timer_updated.connect(self._on_timer_updated)
        self.controller.question_loaded.connect(self._on_question_loaded)
        self.answer_submitted.connect(self.controller.check_answer)

    def setup_ui(self):
        """Initialize the UI components"""
        try:
            self.setWindowTitle("Quiz Screen")
            self.setGeometry(100, 100, 800, 600)

            main_layout = QVBoxLayout()
            main_layout.setContentsMargins(40, 40, 40, 40)
            main_layout.setSpacing(20)

            # Title with enterprise styling
            title = QLabel("Quiz")
            title.setAlignment(Qt.AlignCenter)
            title.setStyleSheet(self.style_manager.get_style('heading_1'))
            main_layout.addWidget(title)

            # Timer label with enterprise styling
            self.timer_label = QLabel("Time remaining: --:--")
            self.timer_label.setAlignment(Qt.AlignRight)
            timer_style = f"""
                QLabel {{
                    color: {self.design_system.color('text_secondary')};
                    font-size: {self.design_system.font_size('lg')}px;
                    font-weight: {self.design_system._typography['font_weight_medium']};
                    padding: {self.design_system.spacing('sm')}px 0px;
                }}
            """
            self.timer_label.setStyleSheet(timer_style)
            main_layout.addWidget(self.timer_label)

            # Progress bar with enterprise styling
            self.progress_bar = QProgressBar()
            self.progress_bar.setTextVisible(True)
            self.progress_bar.setAlignment(Qt.AlignCenter)
            progress_style = f"""
                QProgressBar {{
                    background: {self.design_system.color('surface')};
                    border: 2px solid {self.design_system.color('border')};
                    border-radius: {self.design_system.radius('md')}px;
                    text-align: center;
                    color: {self.design_system.color('text_primary')};
                    font-size: {self.design_system.font_size('sm')}px;
                    font-weight: {self.design_system._typography['font_weight_medium']};
                    min-height: 24px;
                }}
                QProgressBar::chunk {{
                    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                        stop: 0 {self.design_system.color('primary')},
                        stop: 1 {self.design_system.color('primary_hover')});
                    border-radius: {self.design_system.radius('sm')}px;
                }}
            """
            self.progress_bar.setStyleSheet(progress_style)
            main_layout.addWidget(self.progress_bar)

            # Question card with enterprise styling
            question_card = QFrame()
            question_card.setStyleSheet(self.style_manager.get_style('card'))
            question_layout = QVBoxLayout(question_card)

            # Question display with enterprise styling
            self.question_label = QTextEdit()
            self.question_label.setReadOnly(True)
            self.question_label.setMinimumHeight(100)
            question_text_style = f"""
                QTextEdit {{
                    background-color: transparent;
                    color: {self.design_system.color('text_primary')};
                    border: none;
                    font-size: {self.design_system.font_size('lg')}px;
                    font-weight: {self.design_system._typography['font_weight_normal']};
                    line-height: {self.design_system._typography['line_height_normal']};
                    padding: {self.design_system.spacing('md')}px;
                }}
            """
            self.question_label.setStyleSheet(question_text_style)
            question_layout.addWidget(self.question_label)

            # Options section with enterprise styling
            self.option_group = QButtonGroup(self)
            self.option_buttons = []
            options_layout = QVBoxLayout()

            # Create enterprise option button style
            option_style = f"""
                QRadioButton {{
                    color: {self.design_system.color('text_primary')};
                    font-size: {self.design_system.font_size('base')}px;
                    font-weight: {self.design_system._typography['font_weight_normal']};
                    padding: {self.design_system.spacing('md')}px;
                    margin: {self.design_system.spacing('xs')}px 0px;
                    border-radius: {self.design_system.radius('md')}px;
                    background: transparent;
                }}
                QRadioButton:hover {{
                    background-color: {self.design_system.color('hover')};
                    color: {self.design_system.color('primary')};
                }}
                QRadioButton:checked {{
                    color: {self.design_system.color('primary')};
                    font-weight: {self.design_system._typography['font_weight_medium']};
                    background-color: {self.design_system.color('surface_hover')};
                }}
                QRadioButton::indicator {{
                    width: 18px;
                    height: 18px;
                    border-radius: 9px;
                    border: 2px solid {self.design_system.color('border')};
                    background: {self.design_system.color('surface')};
                }}
                QRadioButton::indicator:checked {{
                    background: {self.design_system.color('primary')};
                    border-color: {self.design_system.color('primary')};
                }}
            """

            for i in range(4):
                btn = QRadioButton(f"Option {chr(ord('A') + i)}")
                btn.setStyleSheet(option_style)
                self.option_buttons.append(btn)
                self.option_group.addButton(btn, i)
                options_layout.addWidget(btn)
            question_layout.addLayout(options_layout)

            main_layout.addWidget(question_card)

            # Add enhanced MCQ widget if enabled
            if self.use_enhanced_widget:
                try:
                    self.enhanced_mcq_widget = EnhancedMCQWidget(self)
                    self.enhanced_mcq_widget.option_selected.connect(self._on_enhanced_option_selected)
                    self.enhanced_mcq_widget.setVisible(False)  # Initially hidden
                    main_layout.addWidget(self.enhanced_mcq_widget)
                    logger.info("✅ Enhanced MCQ widget initialized")
                except Exception as e:
                    logger.error(f"❌ Failed to initialize enhanced MCQ widget: {e}")
                    self.use_enhanced_widget = False

            # Submit button with enterprise styling
            self.submit_btn = QPushButton("Submit Answer")
            self.submit_btn.setCursor(Qt.PointingHandCursor)
            self.submit_btn.clicked.connect(self.check_answer)
            self.submit_btn.setStyleSheet(self.style_manager.get_style('button_success'))
            main_layout.addWidget(self.submit_btn)

            # Feedback section with enterprise styling
            self.feedback_label = QLabel()
            self.feedback_label.setAlignment(Qt.AlignCenter)
            self.feedback_label.setWordWrap(True)
            self.feedback_label.setVisible(False)
            feedback_style = f"""
                QLabel {{
                    color: {self.design_system.color('primary')};
                    font-size: {self.design_system.font_size('lg')}px;
                    font-weight: {self.design_system._typography['font_weight_semibold']};
                    padding: {self.design_system.spacing('md')}px;
                    background-color: {self.design_system.color('surface')};
                    border: 1px solid {self.design_system.color('border')};
                    border-radius: {self.design_system.radius('lg')}px;
                    margin: {self.design_system.spacing('sm')}px 0px;
                }}
            """
            self.feedback_label.setStyleSheet(feedback_style)
            main_layout.addWidget(self.feedback_label)

            # Explanation section with enterprise styling
            explanation_card = QFrame()
            explanation_card.setVisible(False)
            explanation_card.setStyleSheet(self.style_manager.get_style('card'))
            explanation_layout = QVBoxLayout(explanation_card)

            self.explanation_header_label = QLabel("Explanation:")
            self.explanation_header_label.setStyleSheet(self.style_manager.get_style('heading_4'))
            explanation_layout.addWidget(self.explanation_header_label)

            self.explanation_area = QTextEdit()
            self.explanation_area.setReadOnly(True)
            explanation_text_style = f"""
                QTextEdit {{
                    background-color: transparent;
                    color: {self.design_system.color('text_primary')};
                    border: none;
                    font-size: {self.design_system.font_size('base')}px;
                    font-weight: {self.design_system._typography['font_weight_normal']};
                    line-height: {self.design_system._typography['line_height_normal']};
                    padding: {self.design_system.spacing('sm')}px;
                }}
            """
            self.explanation_area.setStyleSheet(explanation_text_style)
            explanation_layout.addWidget(self.explanation_area)

            main_layout.addWidget(explanation_card)
            self.explanation_card = explanation_card

            # Navigation buttons
            nav_layout = QHBoxLayout()
            nav_layout.setSpacing(10)
            
            self.prev_history_btn = QPushButton("Previous Question")
            self.prev_history_btn.setCursor(Qt.PointingHandCursor)
            self.prev_history_btn.clicked.connect(self.show_prev_answered)
            self.prev_history_btn.setVisible(False)
            self.prev_history_btn.setStyleSheet(self.style_manager.get_style('button_secondary'))
            nav_layout.addWidget(self.prev_history_btn)

            self.next_history_btn = QPushButton("Next Question")
            self.next_history_btn.setCursor(Qt.PointingHandCursor)
            self.next_history_btn.clicked.connect(self.show_next_answered)
            self.next_history_btn.setVisible(False)
            self.next_history_btn.setStyleSheet(self.style_manager.get_style('button_secondary'))
            nav_layout.addWidget(self.next_history_btn)

            self.next_new_question_btn = QPushButton("Next Question")
            self.next_new_question_btn.setCursor(Qt.PointingHandCursor)
            self.next_new_question_btn.clicked.connect(self.load_next_new_question_trigger)
            self.next_new_question_btn.setVisible(False)
            self.next_new_question_btn.setStyleSheet(self.style_manager.get_style('button_primary'))
            nav_layout.addWidget(self.next_new_question_btn)

            main_layout.addLayout(nav_layout)

            # Back to menu button with enterprise styling
            self.back_to_menu_btn = QPushButton("Back to Main Menu")
            self.back_to_menu_btn.setCursor(Qt.PointingHandCursor)
            self.back_to_menu_btn.clicked.connect(self.go_to_main_menu)
            self.back_to_menu_btn.setStyleSheet(self.style_manager.get_style('button_secondary'))
            main_layout.addWidget(self.back_to_menu_btn)

            self.setLayout(main_layout)

            # Apply enterprise styling to the main widget
            self.apply_enterprise_styling()

        except Exception as e:
            logger.error(f"Error setting up quiz UI: {e}", exc_info=True)
            self.show_error_dialog("UI Error", f"Failed to set up quiz interface: {e}")

    def apply_enterprise_styling(self):
        """Apply enterprise styling to the quiz screen"""
        try:
            # Apply main widget styling
            main_widget_style = f"""
                QWidget {{
                    background: {self.design_system.color('bg_primary')};
                    color: {self.design_system.color('text_primary')};
                    font-family: {self.design_system._typography['font_family_primary']};
                }}
            """
            self.setStyleSheet(main_widget_style)

            logger.info("✅ Enterprise styling applied to quiz screen")

        except Exception as e:
            logger.warning(f"Enterprise styling failed for quiz screen, using fallback: {e}")
            # Fallback to basic styling
            self.setStyleSheet("""
                QWidget {
                    background-color: #1a1b1e;
                    color: white;
                }
            """)

    def check_answer(self):
        """Handle submission of an answer - delegates to controller."""
        if not self.current_question_data:
            self.feedback_label.setText("Error: No question data available")
            self.feedback_label.setVisible(True)
            return

        # Find selected option
        selected_option_index = -1
        for i, btn in enumerate(self.option_buttons):
            if btn.isChecked():
                selected_option_index = i
                break

        if selected_option_index == -1:
            self.feedback_label.setText("Please select an answer before submitting")
            self.feedback_label.setVisible(True)
            return

        # Convert to letter and emit to controller
        selected_option_letter = chr(ord('A') + selected_option_index)
        self.answer_submitted.emit(selected_option_letter)

    def _on_answer_checked(self, is_correct: bool, correct_answer: str):
        """Handle answer check result from controller"""
        # Show feedback
        feedback_text = "Correct! 🎉" if is_correct else f"Incorrect. The correct answer was {correct_answer}."
        self.feedback_label.setText(feedback_text)
        self.feedback_label.setVisible(True)

        # Show explanation
        if 'explanation' in self.current_question_data:
            self.explanation_card.setVisible(True)
            self.explanation_area.setPlainText(self.current_question_data['explanation'])

        # Disable options and submit button
        for btn in self.option_buttons:
            btn.setEnabled(False)
        self.submit_btn.setEnabled(False)

        # Show navigation buttons
        self.next_new_question_btn.setVisible(True)

        # Update navigation buttons if they exist
        for attr in ['prev_history_btn', 'next_history_btn', 'next_new_question_btn', 'back_to_menu_btn']:
            if hasattr(self, attr):
                getattr(self, attr).setVisible(True)

    def _on_timer_updated(self, remaining_seconds: int):
        """Handle timer update from controller"""
        minutes = remaining_seconds // 60
        seconds = remaining_seconds % 60
        self.timer_label.setText(f"Time remaining: {minutes:02d}:{seconds:02d}")

    def _on_question_loaded(self, question_data: Dict[str, Any]):
        """Handle question loaded from controller"""
        self.current_question_data = question_data
        self.display_question(question_data)

    def _on_enhanced_option_selected(self, option_index: int):
        """Handle option selection from enhanced MCQ widget"""
        try:
            # Convert index to letter
            selected_option_letter = chr(ord('A') + option_index)

            # Auto-submit the answer
            self.answer_submitted.emit(selected_option_letter)

            # Show explanation in enhanced widget
            if self.current_question_data:
                correct_answer = self.current_question_data.get('correct', 'A')
                is_correct = selected_option_letter == correct_answer
                self.enhanced_mcq_widget.show_explanation(is_correct, correct_answer)

        except Exception as e:
            logger.error(f"Error handling enhanced option selection: {e}")

    def display_answered_question(self, entry, current_index, total_count):
        """Display a previously answered question with feedback."""
        # Hide submit button during review
        if hasattr(self, 'submit_btn'):
            self.submit_btn.setVisible(False)

        # Disable option selection during review
        if hasattr(self, 'option_buttons'):
            for btn in self.option_buttons:
                btn.setChecked(False)
                btn.setEnabled(False)

        # Extract data from history entry
        q_data = entry['question_data']
        user_answer_letter = entry.get('user_answer_letter', 'None').upper()
        is_correct = entry.get('is_correct', False)
        timed_out = entry.get('timed_out', False)

        # Display question
        self.current_question_data = q_data
        self.question_label.setText(q_data.get('question_text', 'Error: Question text not found'))

        # Update options and highlight correct/incorrect answers
        options = q_data.get('options', [])
        if isinstance(options, str):
            options = options.split('|')
        
        correct_option_letter = q_data.get('correct_option_letter', '').upper()

        for i, btn in enumerate(self.option_buttons):
            if i < len(options):
                option_letter = chr(ord('A') + i)
                btn.setText(f"{option_letter}) {options[i]}")
                btn.setVisible(True)

                # Style the button based on correctness using enterprise colors
                style = f"""
                    QRadioButton {{
                        color: {self.design_system.color('text_primary')};
                        font-size: {self.design_system.font_size('base')}px;
                        padding: {self.design_system.spacing('md')}px;
                        margin: {self.design_system.spacing('xs')}px 0px;
                        border-radius: {self.design_system.radius('md')}px;
                    }}
                """

                if option_letter == user_answer_letter:
                    btn.setChecked(True)
                    if is_correct:
                        style = f"""
                            QRadioButton {{
                                color: {self.design_system.color('success')};
                                font-size: {self.design_system.font_size('base')}px;
                                font-weight: {self.design_system._typography['font_weight_semibold']};
                                padding: {self.design_system.spacing('md')}px;
                                margin: {self.design_system.spacing('xs')}px 0px;
                                border-radius: {self.design_system.radius('md')}px;
                                background-color: {self.design_system.color('surface_hover')};
                            }}
                        """
                    else:
                        style = f"""
                            QRadioButton {{
                                color: {self.design_system.color('danger')};
                                font-size: {self.design_system.font_size('base')}px;
                                font-weight: {self.design_system._typography['font_weight_semibold']};
                                padding: {self.design_system.spacing('md')}px;
                                margin: {self.design_system.spacing('xs')}px 0px;
                                border-radius: {self.design_system.radius('md')}px;
                                background-color: {self.design_system.color('surface_hover')};
                            }}
                        """
                elif option_letter == correct_option_letter:
                    style = f"""
                        QRadioButton {{
                            color: {self.design_system.color('success')};
                            font-size: {self.design_system.font_size('base')}px;
                            font-weight: {self.design_system._typography['font_weight_semibold']};
                            padding: {self.design_system.spacing('md')}px;
                            margin: {self.design_system.spacing('xs')}px 0px;
                            border-radius: {self.design_system.radius('md')}px;
                            background-color: {self.design_system.color('surface_hover')};
                        }}
                    """
                
                btn.setStyleSheet(style)
            else:
                btn.setVisible(False)

        # Show feedback
        feedback_text = ""
        if timed_out:
            feedback_text = f"Time's up! The correct answer was {correct_option_letter}."
        else:
            feedback_text = "Correct! 🎉" if is_correct else f"Incorrect. The correct answer was {correct_option_letter}."
        
        self.feedback_label.setText(feedback_text)
        self.feedback_label.setVisible(True)

        # Show explanation
        if 'explanation' in q_data:
            self.explanation_card.setVisible(True)
            self.explanation_area.setPlainText(q_data['explanation'])

        # Update navigation buttons
        if hasattr(self, 'prev_history_btn'):
            self.prev_history_btn.setVisible(True)
            self.prev_history_btn.setEnabled(current_index > 0)
        
        if hasattr(self, 'next_history_btn'):
            self.next_history_btn.setVisible(True)
            self.next_history_btn.setEnabled(current_index < total_count - 1)
        
        if hasattr(self, 'next_new_question_btn'):
            self.next_new_question_btn.setVisible(True)
            self.next_new_question_btn.setText("Exit Review & New Question")
        
        if hasattr(self, 'back_to_menu_btn'):
            self.back_to_menu_btn.setVisible(True)

    def update_timer(self):
        """Update the timer display and handle timer expiration."""
        if not self.timer_active:
            return
            
        self.remaining_time -= 1
        if hasattr(self, 'timer_label'):
            minutes = self.remaining_time // 60
            seconds = self.remaining_time % 60
            self.timer_label.setText(f"Time remaining: {minutes:02d}:{seconds:02d}")

        if self.remaining_time <= 0:
            self.handle_time_up()

    def handle_time_up(self):
        """Handle what happens when time runs out."""
        self.timer_active = False
        self.timer.stop()
        if hasattr(self, 'timer_label'):
            self.timer_label.setText("Time's up!")

        # Automatically submit current answer if one is selected
        selected = False
        for btn in self.option_buttons:
            if btn.isChecked():
                selected = True
                break
        
        if selected:
            self.check_answer()
        else:
            QMessageBox.warning(self, "Time's Up!", "Time's up! The question will be marked as incorrect.")
            # Create a "timed out" answer entry
            if self.current_question_data and hasattr(self.parent, 'add_to_history'):
                timeout_data = {
                    'question_data': self.current_question_data,
                    'user_answer_letter': 'None',
                    'is_correct': False,
                    'timestamp': datetime.now().isoformat(),
                    'timed_out': True
                }
                self.parent.add_to_history(timeout_data)

            # Disable options and show the correct answer
            for btn in self.option_buttons:
                btn.setEnabled(False)
            self.submit_btn.setEnabled(False)
            
            if self.current_question_data:
                correct_letter = self.current_question_data.get('correct_option_letter', '').upper()
                self.feedback_label.setText(f"Time's up! The correct answer was {correct_letter}.")
                self.feedback_label.setVisible(True)

    def start_timer(self, duration_seconds):
        """Start the quiz timer with given duration."""
        self.remaining_time = duration_seconds
        self.timer_active = True
        self.timer.start(1000)  # Update every second

    def stop_timer(self):
        """Stop the quiz timer."""
        self.timer_active = False
        self.timer.stop()

    def display_question(self, question_data: Dict[str, Any]):
        """
        Display a new question using enhanced widget if available

        Args:
            question_data: Dictionary containing question text and options
        """
        try:
            self.current_question_data = question_data

            # Use enhanced widget if available and enabled
            if self.use_enhanced_widget and self.enhanced_mcq_widget:
                try:
                    # Hide traditional UI elements
                    if hasattr(self, 'question_label'):
                        self.question_label.setVisible(False)
                    for btn in self.option_buttons:
                        btn.setVisible(False)
                    if hasattr(self, 'submit_btn'):
                        self.submit_btn.setVisible(False)

                    # Show and use enhanced widget
                    self.enhanced_mcq_widget.setVisible(True)
                    self.enhanced_mcq_widget.display_mcq(question_data)
                    self.enhanced_mcq_widget.clear_selection()

                    logger.info("✅ Question displayed using enhanced MCQ widget")
                    return

                except Exception as e:
                    logger.error(f"❌ Enhanced widget failed, falling back to traditional UI: {e}")
                    self.use_enhanced_widget = False

            # Fallback to traditional UI
            self._display_question_traditional(question_data)

        except Exception as e:
            logger.error(f"Error displaying question: {e}")

    def _display_question_traditional(self, question_data: Dict[str, Any]):
        """Display question using traditional UI elements"""
        try:
            # Show traditional UI elements
            if self.enhanced_mcq_widget:
                self.enhanced_mcq_widget.setVisible(False)
            if hasattr(self, 'question_label'):
                self.question_label.setVisible(True)
            if hasattr(self, 'submit_btn'):
                self.submit_btn.setVisible(True)

            # Update question text
            self.question_label.setText(question_data.get('question', ''))

            # Clear existing options
            self.clear_question()

            # Add new options
            options = question_data.get('options', [])
            for i, option in enumerate(options):
                if i < len(self.option_buttons):
                    btn = self.option_buttons[i]
                    btn.setText(f"{chr(ord('A') + i)}) {option}")
                    btn.setVisible(True)
                    btn.setEnabled(True)
                    btn.setChecked(False)

            # Update progress if available
            if 'progress' in question_data:
                self.progress_bar.setValue(question_data['progress'])
            
        except Exception as e:
            logger.error(f"Error displaying question: {e}", exc_info=True)
            self.show_error_dialog("Question Error", f"Failed to display question: {e}")

    def show_error_dialog(self, title: str, message: str):
        """Show an error dialog to the user"""
        try:
            QMessageBox.critical(self, title, message)
        except Exception as e:
            logger.error(f"Failed to show error dialog: {e}")
            self.question_label.setText(f"Error: {message}")

    def clear_question(self):
        """Clear the current question display"""
        try:
            self.question_label.setText("")
            
            # Clear option buttons
            while self.option_buttons:
                btn = self.option_buttons.pop(0)
                btn.deleteLater()
            
            # Reset submit button
            if self.submit_btn:
                self.submit_btn.setEnabled(False)
            
        except Exception as e:
            logger.error(f"Error clearing question: {e}", exc_info=True)

    def option_selected(self):
        """Handle option selection event"""
        try:
            if self.submit_btn:
                self.submit_btn.setEnabled(True)
        except Exception as e:
            logger.error(f"Error handling option selection: {e}", exc_info=True)

    def load_next_new_question_trigger(self):
        """Trigger loading of a new question."""
        if hasattr(self.parent, 'load_new_question'):
            self.parent.load_new_question()

    def show_prev_answered(self):
        """Show the previous answered question"""
        if hasattr(self.parent, 'show_previous_question'):
            self.parent.show_previous_question()

    def show_next_answered(self):
        """Show the next answered question"""
        if hasattr(self.parent, 'show_next_question'):
            self.parent.show_next_question()

    def go_to_main_menu(self):
        """Return to main menu"""
        if hasattr(self.parent, 'stack'):
            if hasattr(self.parent, 'main_menu'):
                self.parent.stack.setCurrentWidget(self.parent.main_menu)

    def cleanup(self):
        """Clean up resources"""
        try:
            self.clear_question()
            self.current_question_data = None
            
            # Clean up signals
            try:
                self.question_answered.disconnect()
                self.quiz_completed.disconnect()
            except Exception:
                pass  # Ignore disconnection errors
                
        except Exception as e:
            logger.error(f"Error during cleanup: {e}", exc_info=True)