"""
Style Migration Helper

This module provides utilities to help migrate from legacy styling systems
(AppStyles, ProfessionalStyles) to the enterprise design system.

Features:
- Style mapping between legacy and enterprise systems
- Automated style conversion
- Backward compatibility layer
- Migration validation
"""

from typing import Dict, Any, Optional, List
import re
import logging

from .enterprise_style_manager import get_style_manager, EnterpriseStyleManager
from .enterprise_design_system import get_design_system, ComponentVariant, ComponentSize

logger = logging.getLogger(__name__)

class StyleMigrationHelper:
    """
    Helper class for migrating from legacy styling to enterprise design system.
    """
    
    def __init__(self):
        self.style_manager = get_style_manager()
        self.design_system = get_design_system()
        
        # Mapping from legacy style methods to enterprise equivalents
        self.legacy_to_enterprise_mapping = {
            # AppStyles mappings
            'AppStyles.get_button_style()': 'primary_button',
            'AppStyles.get_card_style()': 'card',
            'AppStyles.get_input_style()': 'input_field',
            'AppStyles.get_label_style()': 'label',
            'AppStyles.get_main_window_style()': 'main_window',
            
            # ProfessionalStyles mappings
            'ProfessionalStyles.get_primary_button_style()': 'primary_button',
            'ProfessionalStyles.get_secondary_button_style()': 'secondary_button',
            'ProfessionalStyles.get_card_style()': 'card',
            'ProfessionalStyles.get_input_style()': 'input_field',
            'ProfessionalStyles.get_main_window_style()': 'main_window',
            'ProfessionalStyles.get_menu_style()': 'menu',
            'ProfessionalStyles.get_dialog_style()': 'dialog',
        }
        
        # Color mappings
        self.color_mappings = {
            # AppStyles colors
            'AppStyles.COLORS[\'PRIMARY_COLOR\']': 'primary.main',
            'AppStyles.COLORS[\'SECONDARY_COLOR\']': 'secondary.main',
            'AppStyles.COLORS[\'BACKGROUND_COLOR\']': 'background.primary',
            'AppStyles.COLORS[\'TEXT_PRIMARY\']': 'text.primary',
            'AppStyles.COLORS[\'TEXT_SECONDARY\']': 'text.secondary',
            
            # ProfessionalStyles colors
            'ProfessionalStyles.COLORS[\'PRIMARY_COLOR\']': 'primary.main',
            'ProfessionalStyles.COLORS[\'SECONDARY_COLOR\']': 'secondary.main',
            'ProfessionalStyles.COLORS[\'BACKGROUND_PRIMARY\']': 'background.primary',
            'ProfessionalStyles.COLORS[\'TEXT_PRIMARY\']': 'text.primary',
            'ProfessionalStyles.COLORS[\'TEXT_SECONDARY\']': 'text.secondary',
        }
    
    def migrate_style_call(self, legacy_call: str) -> str:
        """
        Convert a legacy style method call to enterprise equivalent.
        
        Args:
            legacy_call: Legacy style method call (e.g., 'AppStyles.get_button_style()')
            
        Returns:
            Enterprise style manager call
        """
        if legacy_call in self.legacy_to_enterprise_mapping:
            component_name = self.legacy_to_enterprise_mapping[legacy_call]
            return f"self.style_manager.get_style('{component_name}')"
        
        logger.warning(f"No mapping found for legacy style call: {legacy_call}")
        return legacy_call
    
    def migrate_color_reference(self, legacy_color: str) -> str:
        """
        Convert a legacy color reference to enterprise equivalent.
        
        Args:
            legacy_color: Legacy color reference
            
        Returns:
            Enterprise color reference
        """
        if legacy_color in self.color_mappings:
            color_path = self.color_mappings[legacy_color]
            return f"self.design_system.get_color('{color_path}')"
        
        logger.warning(f"No mapping found for legacy color: {legacy_color}")
        return legacy_color
    
    def generate_migration_code(self, component_name: str) -> str:
        """
        Generate the code needed to add enterprise styling to a component.
        
        Args:
            component_name: Name of the component class
            
        Returns:
            Code snippet for enterprise styling setup
        """
        return f'''
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Enterprise styling setup
        self.style_manager = get_style_manager()
        self.design_system = get_design_system()
        
        # Apply enterprise styling
        self.apply_enterprise_styling()
    
    def apply_enterprise_styling(self):
        """Apply enterprise styling to this component"""
        # Apply component-specific styling
        main_style = self.style_manager.get_style('{component_name.lower()}')
        self.setStyleSheet(main_style)
        
        # Apply global styles if needed
        self.style_manager.apply_global_styles()
'''
    
    def create_backward_compatibility_layer(self) -> str:
        """
        Create a backward compatibility layer for legacy styling.
        
        Returns:
            Code for backward compatibility
        """
        return '''
# Backward compatibility layer for legacy styling
class LegacyStyleAdapter:
    """Adapter to provide backward compatibility for legacy styling calls"""
    
    def __init__(self):
        self.style_manager = get_style_manager()
        self.design_system = get_design_system()
    
    @property
    def COLORS(self):
        """Legacy color access"""
        return {
            'PRIMARY_COLOR': self.design_system.get_color('primary.main'),
            'SECONDARY_COLOR': self.design_system.get_color('secondary.main'),
            'BACKGROUND_COLOR': self.design_system.get_color('background.primary'),
            'BACKGROUND_PRIMARY': self.design_system.get_color('background.primary'),
            'TEXT_PRIMARY': self.design_system.get_color('text.primary'),
            'TEXT_SECONDARY': self.design_system.get_color('text.secondary'),
        }
    
    def get_button_style(self):
        return self.style_manager.get_style('primary_button')
    
    def get_primary_button_style(self):
        return self.style_manager.get_style('primary_button')
    
    def get_secondary_button_style(self):
        return self.style_manager.get_style('secondary_button')
    
    def get_card_style(self):
        return self.style_manager.get_style('card')
    
    def get_input_style(self):
        return self.style_manager.get_style('input_field')
    
    def get_main_window_style(self):
        return self.style_manager.get_style('main_window')

# Create legacy adapters
AppStyles = LegacyStyleAdapter()
ProfessionalStyles = LegacyStyleAdapter()
'''
    
    def validate_migration(self, file_path: str) -> List[str]:
        """
        Validate that a file has been properly migrated.
        
        Args:
            file_path: Path to the file to validate
            
        Returns:
            List of validation issues found
        """
        issues = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Check for legacy imports
            legacy_imports = [
                'from .styles import AppStyles',
                'from .professional_styles import ProfessionalStyles',
                'import styles',
                'import professional_styles'
            ]
            
            for legacy_import in legacy_imports:
                if legacy_import in content:
                    issues.append(f"Legacy import found: {legacy_import}")
            
            # Check for legacy style calls
            legacy_patterns = [
                r'AppStyles\.',
                r'ProfessionalStyles\.',
                r'\.get_button_style\(\)',
                r'\.get_card_style\(\)',
            ]
            
            for pattern in legacy_patterns:
                matches = re.findall(pattern, content)
                if matches:
                    issues.append(f"Legacy style calls found: {pattern} ({len(matches)} occurrences)")
            
            # Check for enterprise imports
            enterprise_imports = [
                'from .enterprise_style_manager import get_style_manager',
                'from .enterprise_design_system import get_design_system'
            ]
            
            has_enterprise_imports = any(imp in content for imp in enterprise_imports)
            if not has_enterprise_imports and any(pattern in content for pattern in ['setStyleSheet', 'QStyleSheet']):
                issues.append("File uses styling but missing enterprise imports")
        
        except Exception as e:
            issues.append(f"Error reading file: {e}")
        
        return issues

# Global migration helper instance
_migration_helper = None

def get_migration_helper() -> StyleMigrationHelper:
    """Get the global style migration helper instance"""
    global _migration_helper
    if _migration_helper is None:
        _migration_helper = StyleMigrationHelper()
    return _migration_helper
