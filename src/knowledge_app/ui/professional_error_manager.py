"""
Professional Error Manager - User-Friendly Error Experience

This module provides a comprehensive error management system that transforms
technical errors into user-friendly experiences with actionable guidance.

Features:
- Professional error dialogs with solutions
- Smart error categorization and messaging
- Integration with toast notifications
- Context-aware error handling
- Recovery suggestions and actions
"""

import logging
from typing import Dict, List, Optional, Callable, Any
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QObject, pyqtSignal

from .professional_error_dialogs import ProfessionalErrorDialog, ErrorSeverity, ErrorCategory
from .seductive_transitions import ProfessionalToastNotification

logger = logging.getLogger(__name__)

class ProfessionalErrorManager(QObject):
    """
    Professional error manager that provides user-friendly error handling.
    
    Features:
    - Context-aware error messages
    - Actionable solutions and guidance
    - Integration with professional dialogs and toasts
    - Smart error categorization
    - Recovery action suggestions
    """
    
    # Signals
    error_handled = pyqtSignal(str, str, str)  # category, severity, message
    recovery_attempted = pyqtSignal(str, bool)  # action, success
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # Error handling components
        self.toast_system = None
        self.current_dialog = None
        
        # Error message templates
        self.error_templates = self._init_error_templates()
        self.solution_templates = self._init_solution_templates()
        self.action_templates = self._init_action_templates()
        
        # Recovery handlers
        self.recovery_handlers = {}
        
    def set_toast_system(self, toast_system: ProfessionalToastNotification):
        """Set the toast notification system"""
        self.toast_system = toast_system
    
    def handle_error(self, error: Exception, context: str = "", 
                    severity: str = ErrorSeverity.ERROR,
                    show_dialog: bool = None, 
                    additional_data: Dict = None) -> None:
        """
        Handle an error with professional user experience.
        
        Args:
            error: The exception that occurred
            context: Context where the error occurred
            severity: Error severity level
            show_dialog: Whether to show dialog (auto-determined if None)
            additional_data: Additional context data
        """
        try:
            # Categorize error
            category = self._categorize_error(error)
            
            # Get user-friendly message and solutions
            user_message = self._get_user_friendly_message(error, category, context)
            solutions = self._get_solutions(error, category, context)
            actions = self._get_actions(error, category, context)
            
            # Determine how to show error
            if show_dialog is None:
                show_dialog = self._should_show_dialog(severity, category)
            
            if show_dialog:
                self._show_professional_dialog(
                    error, user_message, category, severity, 
                    context, solutions, actions, additional_data
                )
            else:
                self._show_toast_notification(user_message, severity)
            
            # Emit signal
            self.error_handled.emit(category, severity, user_message)
            
            logger.info(f"Professional error handled: {category} - {severity}")
            
        except Exception as e:
            logger.error(f"Error in professional error manager: {e}")
            # Fallback to simple message
            self._show_fallback_error(str(error))
    
    def handle_network_error(self, error: Exception, context: str = ""):
        """Handle network-specific errors"""
        self.handle_error(
            error, 
            context or "Network Operation", 
            ErrorSeverity.WARNING,
            show_dialog=False  # Use toast for network errors
        )
    
    def handle_model_error(self, error: Exception, model_name: str = ""):
        """Handle AI model-specific errors"""
        context = f"AI Model: {model_name}" if model_name else "AI Model Operation"
        self.handle_error(
            error,
            context,
            ErrorSeverity.ERROR,
            show_dialog=True,  # Models are important, show dialog
            additional_data={'model_name': model_name}
        )
    
    def handle_file_error(self, error: Exception, file_path: str = ""):
        """Handle file operation errors"""
        context = f"File: {file_path}" if file_path else "File Operation"
        self.handle_error(
            error,
            context,
            ErrorSeverity.ERROR,
            additional_data={'file_path': file_path}
        )
    
    def handle_gpu_error(self, error: Exception, context: str = ""):
        """Handle GPU/CUDA errors"""
        self.handle_error(
            error,
            context or "GPU Operation",
            ErrorSeverity.ERROR,
            show_dialog=True,  # GPU errors need attention
            additional_data={'gpu_related': True}
        )
    
    def show_success_message(self, message: str, duration: int = 3000):
        """Show success message via toast"""
        if self.toast_system:
            self.toast_system.show_success(message, duration)
    
    def show_info_message(self, message: str, duration: int = 3000):
        """Show info message via toast"""
        if self.toast_system:
            self.toast_system.show_info(message, duration)
    
    def show_warning_message(self, message: str, duration: int = 4000):
        """Show warning message via toast"""
        if self.toast_system:
            self.toast_system.show_warning(message, duration)
    
    def _categorize_error(self, error: Exception) -> str:
        """Categorize error based on type and message"""
        error_type = type(error).__name__.lower()
        error_msg = str(error).lower()
        
        # Network errors
        if any(term in error_type for term in ['connection', 'timeout', 'http', 'url']):
            return ErrorCategory.NETWORK
        if any(term in error_msg for term in ['connection', 'network', 'timeout', 'unreachable']):
            return ErrorCategory.NETWORK
            
        # File system errors
        if any(term in error_type for term in ['file', 'io', 'os', 'path']):
            return ErrorCategory.FILE_SYSTEM
        if any(term in error_msg for term in ['file not found', 'permission denied', 'disk space']):
            return ErrorCategory.FILE_SYSTEM
            
        # GPU/CUDA errors
        if any(term in error_msg for term in ['cuda', 'gpu', 'device', 'memory']):
            return ErrorCategory.GPU
            
        # Model errors
        if any(term in error_msg for term in ['model', 'tensor', 'parameter', 'checkpoint']):
            return ErrorCategory.MODEL
            
        # Memory errors
        if any(term in error_type for term in ['memory', 'allocation']):
            return ErrorCategory.MEMORY
        if any(term in error_msg for term in ['out of memory', 'allocation failed']):
            return ErrorCategory.MEMORY
            
        # UI errors
        if any(term in error_type for term in ['qt', 'widget', 'ui']):
            return ErrorCategory.UI
            
        return ErrorCategory.UNKNOWN
    
    def _get_user_friendly_message(self, error: Exception, category: str, context: str) -> str:
        """Get user-friendly error message"""
        templates = self.error_templates.get(category, {})
        error_type = type(error).__name__
        
        # Try to find specific template
        if error_type in templates:
            return templates[error_type].format(context=context, error=str(error))
        
        # Use generic template for category
        generic_template = templates.get('generic', 
            "An error occurred in {context}. Please try again or contact support if the problem persists.")
        
        return generic_template.format(context=context, error=str(error))
    
    def _get_solutions(self, error: Exception, category: str, context: str) -> List[str]:
        """Get suggested solutions for the error"""
        solutions = self.solution_templates.get(category, [])
        
        # Add context-specific solutions
        if category == ErrorCategory.NETWORK:
            solutions.extend([
                "Check your internet connection",
                "Try again in a few moments",
                "Verify firewall settings if the problem persists"
            ])
        elif category == ErrorCategory.MODEL:
            solutions.extend([
                "Restart the application to reload models",
                "Check available disk space",
                "Try using a different model if available"
            ])
        elif category == ErrorCategory.FILE_SYSTEM:
            solutions.extend([
                "Check if the file exists and is accessible",
                "Verify you have the necessary permissions",
                "Ensure sufficient disk space is available"
            ])
        elif category == ErrorCategory.GPU:
            solutions.extend([
                "Update your graphics drivers",
                "Restart the application",
                "Try using CPU mode if available"
            ])
        
        return solutions[:3]  # Limit to 3 solutions for clarity
    
    def _get_actions(self, error: Exception, category: str, context: str) -> List[Dict]:
        """Get action buttons for the error"""
        actions = []
        
        # Add category-specific actions
        if category == ErrorCategory.MODEL:
            actions.append({
                'text': 'Restart Application',
                'action': 'restart_app',
                'type': 'primary',
                'data': {'reason': 'model_error'}
            })
            actions.append({
                'text': 'Use Offline Mode',
                'action': 'enable_offline_mode',
                'type': 'secondary',
                'data': {}
            })
        elif category == ErrorCategory.NETWORK:
            actions.append({
                'text': 'Retry',
                'action': 'retry_operation',
                'type': 'primary',
                'data': {'context': context}
            })
            actions.append({
                'text': 'Work Offline',
                'action': 'enable_offline_mode',
                'type': 'secondary',
                'data': {}
            })
        elif category == ErrorCategory.FILE_SYSTEM:
            actions.append({
                'text': 'Choose Different File',
                'action': 'select_file',
                'type': 'primary',
                'data': {}
            })
        
        return actions
    
    def _should_show_dialog(self, severity: str, category: str) -> bool:
        """Determine whether to show dialog or toast"""
        # Always show dialog for critical errors
        if severity == ErrorSeverity.CRITICAL:
            return True
        
        # Show dialog for important categories
        important_categories = [
            ErrorCategory.MODEL, ErrorCategory.GPU, 
            ErrorCategory.SYSTEM, ErrorCategory.CONFIGURATION
        ]
        
        if category in important_categories:
            return True
        
        # Use toast for less critical errors
        return False
    
    def _show_professional_dialog(self, error: Exception, message: str, 
                                 category: str, severity: str, context: str,
                                 solutions: List[str], actions: List[Dict],
                                 additional_data: Dict = None):
        """Show professional error dialog"""
        try:
            # Create dialog
            self.current_dialog = ProfessionalErrorDialog()
            
            # Connect action signal
            self.current_dialog.action_requested.connect(self._handle_dialog_action)
            
            # Get technical details
            technical_details = f"Error Type: {type(error).__name__}\n"
            technical_details += f"Error Message: {str(error)}\n"
            technical_details += f"Context: {context}\n"
            technical_details += f"Category: {category}\n"
            technical_details += f"Severity: {severity}"
            
            if additional_data:
                technical_details += f"\nAdditional Data: {additional_data}"
            
            # Show dialog
            title = self._get_error_title(category, severity)
            self.current_dialog.show_error(
                title=title,
                message=message,
                category=category,
                severity=severity,
                context=context,
                solutions=solutions,
                actions=actions,
                technical_details=technical_details
            )
            
        except Exception as e:
            logger.error(f"Error showing professional dialog: {e}")
            self._show_fallback_error(message)
    
    def _show_toast_notification(self, message: str, severity: str):
        """Show toast notification for less critical errors"""
        if not self.toast_system:
            return
        
        if severity == ErrorSeverity.WARNING:
            self.toast_system.show_warning(message)
        elif severity == ErrorSeverity.INFO:
            self.toast_system.show_info(message)
        else:
            self.toast_system.show_error(message)
    
    def _show_fallback_error(self, message: str):
        """Show fallback error message"""
        logger.error(f"FALLBACK ERROR: {message}")
        # Could show a simple QMessageBox here as last resort
    
    def _get_error_title(self, category: str, severity: str) -> str:
        """Get appropriate error title"""
        titles = {
            ErrorCategory.NETWORK: "Network Connection Issue",
            ErrorCategory.MODEL: "AI Model Error",
            ErrorCategory.FILE_SYSTEM: "File Access Error",
            ErrorCategory.GPU: "Graphics Processing Error",
            ErrorCategory.MEMORY: "Memory Error",
            ErrorCategory.UI: "Interface Error",
            ErrorCategory.SYSTEM: "System Error",
            ErrorCategory.CONFIGURATION: "Configuration Error",
        }
        
        base_title = titles.get(category, "Application Error")
        
        if severity == ErrorSeverity.CRITICAL:
            return f"Critical {base_title}"
        elif severity == ErrorSeverity.WARNING:
            return f"{base_title} Warning"
        
        return base_title
    
    def _handle_dialog_action(self, action_name: str, action_data: Dict):
        """Handle action from error dialog"""
        try:
            logger.info(f"Handling error dialog action: {action_name}")
            
            # Execute recovery handler if available
            if action_name in self.recovery_handlers:
                handler = self.recovery_handlers[action_name]
                success = handler(action_data)
                self.recovery_attempted.emit(action_name, success)
            else:
                logger.warning(f"No handler for action: {action_name}")
                
        except Exception as e:
            logger.error(f"Error handling dialog action: {e}")
    
    def register_recovery_handler(self, action_name: str, handler: Callable):
        """Register a recovery action handler"""
        self.recovery_handlers[action_name] = handler
    
    def _init_error_templates(self) -> Dict[str, Dict[str, str]]:
        """Initialize error message templates"""
        return {
            ErrorCategory.NETWORK: {
                'generic': "Unable to connect to the internet. Please check your connection and try again.",
                'ConnectionError': "Could not establish a connection. Please check your internet connection.",
                'TimeoutError': "The operation timed out. Please try again or check your connection speed.",
                'HTTPError': "Server communication failed. The service may be temporarily unavailable."
            },
            ErrorCategory.MODEL: {
                'generic': "The AI model encountered an issue. Please restart the application or try a different model.",
                'FileNotFoundError': "AI model files are missing. Please reinstall the application or download the required models.",
                'RuntimeError': "The AI model failed to process your request. Please try again or restart the application.",
                'OutOfMemoryError': "Not enough memory to run the AI model. Please close other applications and try again."
            },
            ErrorCategory.FILE_SYSTEM: {
                'generic': "File operation failed. Please check file permissions and available disk space.",
                'FileNotFoundError': "The requested file could not be found. Please check the file path and try again.",
                'PermissionError': "Access denied. Please check file permissions or run as administrator.",
                'OSError': "File system error occurred. Please check available disk space and file permissions."
            },
            ErrorCategory.GPU: {
                'generic': "Graphics processing error. Please update your drivers or try CPU mode.",
                'RuntimeError': "GPU operation failed. Please update your graphics drivers and restart the application.",
                'OutOfMemoryError': "GPU memory exhausted. Please close other GPU-intensive applications and try again."
            }
        }
    
    def _init_solution_templates(self) -> Dict[str, List[str]]:
        """Initialize solution templates"""
        return {
            ErrorCategory.NETWORK: [
                "Check your internet connection",
                "Try again in a few moments",
                "Contact your network administrator if the problem persists"
            ],
            ErrorCategory.MODEL: [
                "Restart the application",
                "Try using a different AI model",
                "Check available disk space"
            ],
            ErrorCategory.FILE_SYSTEM: [
                "Check file permissions",
                "Verify the file path is correct",
                "Ensure sufficient disk space"
            ],
            ErrorCategory.GPU: [
                "Update graphics drivers",
                "Try using CPU mode",
                "Close other GPU-intensive applications"
            ]
        }
    
    def _init_action_templates(self) -> Dict[str, List[Dict]]:
        """Initialize action templates"""
        return {
            # Will be populated as needed
        }
